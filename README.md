# AI拍照换脸应用

这是一个基于安卓的竖屏（9:16）AI拍照换脸应用。用户可以拍照并选择不同风格的模板，通过AI技术将自己的脸部与模板合成，生成有趣的艺术照片。

## 功能特点

- **视频屏保**：应用启动时显示视频屏保，点击任意位置进入拍照页面
- **相机拍照**：支持摄像头拍照，带倒计时功能
- **风格选择**：提供多种风格分类（古装、现代、学院、职业、民族等）
- **模板浏览**：以网格方式展示各种模板图片
- **AI换脸**：调用云端API进行AI换脸处理
- **结果预览**：展示换脸后的效果图
- **二维码分享**：生成二维码，方便用户扫码获取照片
- **自动返回**：2分钟无操作自动返回屏保页面

## 技术栈

- 开发语言：Java
- 相机功能：CameraX
- 图片加载：Glide
- 视频播放：ExoPlayer
- 网络请求：OkHttp, Retrofit
- 二维码生成：ZXing

## 安装指南

### 前提条件

- Android Studio 4.0+
- Android SDK 24+
- Android设备或模拟器（Android 7.0+）

### 安装步骤

1. 克隆项目到本地
```bash
git clone https://github.com/yourusername/AiartPhoto.git
```

2. 在Android Studio中打开项目

3. 将视频文件放入`app/src/main/res/raw/`目录下，命名为`splash_video.mp4`

4. 添加模板图片到`app/src/main/assets/templates/`目录下，按分类存放
   - 古装：`app/src/main/assets/templates/古装/`
   - 现代：`app/src/main/assets/templates/现代/`
   - 学院：`app/src/main/assets/templates/学院/`
   - 职业：`app/src/main/assets/templates/职业/`
   - 民族：`app/src/main/assets/templates/民族/`

5. 配置API地址（如需使用实际的换脸API）
   - 打开`app/src/main/java/com/jetson/aiartphoto/ResultActivity.java`
   - 修改`API_URL`常量为实际API地址

6. 构建并运行项目

## 使用说明

1. **启动应用**：打开应用，显示视频屏保
2. **拍照**：点击屏保任意位置进入拍照页面，点击相机图标进行拍照
3. **预览照片**：拍照后可以选择"确定"或"重拍"
4. **选择风格**：选择喜欢的风格分类和模板图片
5. **查看结果**：等待AI处理完成后，查看换脸结果
6. **分享/打印**：点击"扫码打印"生成二维码，或点击"返回"回到屏保页面

## 项目结构

```
app/
├── src/main/
│   ├── java/com/jetson/aiartphoto/
│   │   ├── MainActivity.java          # 屏保页面
│   │   ├── CameraActivity.java        # 拍照页面
│   │   ├── StyleSelectionActivity.java # 风格选择页面
│   │   └── ResultActivity.java        # 结果页面
│   ├── res/
│   │   ├── layout/                    # 布局文件
│   │   ├── drawable/                  # 图标和背景
│   │   └── raw/                       # 视频资源
│   ├── assets/
│   │   └── templates/                 # 模板图片目录
│   │       ├── 古装/                  # 古装风格模板
│   │       ├── 现代/                  # 现代风格模板
│   │       ├── 学院/                  # 学院风格模板
│   │       ├── 职业/                  # 职业风格模板
│   │       └── 民族/                  # 民族风格模板
│   └── AndroidManifest.xml            # 应用配置
└── build.gradle                       # 项目依赖
```

## 模板图片要求

1. **图片格式**：支持jpg、jpeg、png格式
2. **图片尺寸**：建议使用9:16比例（竖屏），分辨率不低于1080x1920
3. **文件大小**：单个图片不超过2MB
4. **命名规则**：建议使用数字编号（例如：1.jpg, 2.jpg...）
5. **分类存放**：必须按照分类目录存放，应用会自动识别目录

## 注意事项

1. **摄像头兼容性**：应用支持前置和后置摄像头，以及USB外接摄像头
2. **网络要求**：使用换脸功能需要网络连接
3. **权限**：应用需要相机、存储和网络权限
4. **API配置**：实际使用时需配置有效的换脸API地址
5. **视频资源**：需要添加自己的视频文件到raw目录
6. **模板图片**：需要添加模板图片到assets目录下的对应分类文件夹中

## 问题排查

如遇到以下问题，可尝试解决方案：

1. **摄像头打不开**：检查摄像头权限是否已授予，设备是否支持相机功能
2. **重拍功能无效**：可能是拍照按钮未重新启用，重启应用尝试
3. **风格选择页面崩溃**：可能是模板适配器初始化问题，更新应用到最新版本
4. **模板图片不显示**：检查assets目录下是否正确放置了模板图片，格式是否正确

## 联系方式

如有问题或建议，请联系：<EMAIL> 