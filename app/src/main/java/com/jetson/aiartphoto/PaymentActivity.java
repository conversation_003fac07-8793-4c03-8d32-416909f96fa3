package com.jetson.aiartphoto;

import android.app.Activity;
import android.content.Intent;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.os.Bundle;
import android.os.CountDownTimer;
import android.os.Handler;
import android.os.Looper;
import android.view.View;
import android.widget.Button;
import android.widget.ImageButton;
import android.widget.ImageView;
import android.widget.ProgressBar;
import android.widget.TextView;
import android.widget.Toast;

import androidx.annotation.Nullable;

import com.google.zxing.BarcodeFormat;
import com.google.zxing.EncodeHintType;
import com.google.zxing.WriterException;
import com.google.zxing.common.BitMatrix;
import com.google.zxing.qrcode.QRCodeWriter;
import com.jetson.aiartphoto.config.PaymentConfig;
import com.jetson.aiartphoto.api.WeChatPayApi;

import java.util.EnumMap;
import java.util.Map;

public class PaymentActivity extends Activity {
    
    private static final String TAG = "PaymentActivity";
    
    private ImageView ivQRCode;
    private ProgressBar pbLoading;
    private TextView tvCountdown;
    private TextView tvPaymentStatus;
    private Button btnCancel;
    
    private CountDownTimer countDownTimer;
    private Handler handler;
    private boolean isPaymentSuccess = false;
    private String orderId;
    private String qrCodeUrl;
    
    // 配置和API
    private PaymentConfig config;
    private WeChatPayApi weChatPayApi;
    private int imageCount;
    private int totalAmount; // 总金额（分）

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_payment);
        
        // 初始化配置
        config = PaymentConfig.getInstance(this);
        if (!config.isConfigValid()) {
            Toast.makeText(this, "支付配置错误", Toast.LENGTH_SHORT).show();
            finish();
            return;
        }

        // 初始化API客户端
        weChatPayApi = new WeChatPayApi(config, this);

        initViews();
        initData();
        startPayment();
    }
    
    private void initViews() {
        ivQRCode = findViewById(R.id.ivQRCode);
        pbLoading = findViewById(R.id.pbLoading);
        tvCountdown = findViewById(R.id.tvCountdown);
        tvPaymentStatus = findViewById(R.id.tvPaymentStatus);
        btnCancel = findViewById(R.id.btnCancel);
        ImageButton btnClose = findViewById(R.id.btnClose);

        btnCancel.setOnClickListener(v -> {
            cancelPayment();
        });

        btnClose.setOnClickListener(v -> {
            cancelPayment();
        });
    }
    
    private void initData() {
        handler = new Handler(Looper.getMainLooper());
        
        // 生成订单ID
        orderId = "ORDER_" + System.currentTimeMillis();
        
        // 获取传递过来的图片数量，计算价格
        Intent intent = getIntent();
        imageCount = intent.getIntExtra("image_count", 1);
        totalAmount = imageCount * config.pricePerImage; // 使用配置中的价格
        
        TextView tvAmount = findViewById(R.id.tvAmount);
        tvAmount.setText(String.format("¥ %.2f", totalAmount / 100.0)); // 分转元显示
    }
    
    private void startPayment() {
        // 显示加载状态
        pbLoading.setVisibility(View.VISIBLE);
        ivQRCode.setVisibility(View.GONE);
        
        // 调用微信支付API获取二维码
        generateWeChatQRCode();
        
        // 开始倒计时
        startCountDown();
        
        // 开始轮询支付状态
        startPaymentStatusPolling();
    }
    
    private void generateWeChatQRCode() {
        new Thread(() -> {
            try {
                // 调用真实的微信统一下单API
                String codeUrl = weChatPayApi.createNativeOrder(
                    orderId,
                    config.productName,
                    totalAmount,
                    config.wechatNotifyUrl
                );
                
                if (codeUrl != null && !codeUrl.isEmpty()) {
                    qrCodeUrl = codeUrl;
                    handler.post(() -> {
                        displayQRCode(qrCodeUrl);
                    });
                } else {
                    throw new Exception("获取支付二维码失败");
                }
                
            } catch (Exception e) {
                e.printStackTrace();
                handler.post(() -> {
                    Toast.makeText(this, "生成二维码失败: " + e.getMessage(), Toast.LENGTH_SHORT).show();
                    finish();
                });
            }
        }).start();
    }
    
    private void displayQRCode(String qrCodeUrl) {
        try {
            // 生成二维码
            QRCodeWriter qrCodeWriter = new QRCodeWriter();
            Map<EncodeHintType, Object> hints = new EnumMap<>(EncodeHintType.class);
            hints.put(EncodeHintType.CHARACTER_SET, "UTF-8");
            hints.put(EncodeHintType.MARGIN, 1);
            
            BitMatrix bitMatrix = qrCodeWriter.encode(qrCodeUrl, BarcodeFormat.QR_CODE, 300, 300, hints);
            
            int width = bitMatrix.getWidth();
            int height = bitMatrix.getHeight();
            Bitmap bitmap = Bitmap.createBitmap(width, height, Bitmap.Config.RGB_565);
            
            for (int x = 0; x < width; x++) {
                for (int y = 0; y < height; y++) {
                    bitmap.setPixel(x, y, bitMatrix.get(x, y) ? 0xFF000000 : 0xFFFFFFFF);
                }
            }
            
            // 显示二维码
            ivQRCode.setImageBitmap(bitmap);
            pbLoading.setVisibility(View.GONE);
            ivQRCode.setVisibility(View.VISIBLE);
            
        } catch (WriterException e) {
            e.printStackTrace();
            Toast.makeText(this, "生成二维码失败", Toast.LENGTH_SHORT).show();
            finish();
        }
    }
    
    private void startCountDown() {
        countDownTimer = new CountDownTimer(config.paymentTimeout, 1000) {
            @Override
            public void onTick(long millisUntilFinished) {
                long minutes = millisUntilFinished / 1000 / 60;
                long seconds = (millisUntilFinished / 1000) % 60;
                tvCountdown.setText(String.format("%02d:%02d", minutes, seconds));
            }
            
            @Override
            public void onFinish() {
                if (!isPaymentSuccess) {
                    // 倒计时结束，支付失败
                    tvPaymentStatus.setText("支付超时，返回屏保页面");
                    Toast.makeText(PaymentActivity.this, "支付超时", Toast.LENGTH_SHORT).show();
                    
                    // 延迟1秒后返回屏保页面
                    handler.postDelayed(() -> {
                        Intent intent = new Intent(PaymentActivity.this, MainActivity.class);
                        intent.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP);
                        startActivity(intent);
                        finish();
                    }, 1000);
                }
            }
        }.start();
    }
    
    private void startPaymentStatusPolling() {
        // 使用配置中的轮询间隔，首次延迟2秒开始轮询
        handler.postDelayed(new Runnable() {
            @Override
            public void run() {
                if (!isPaymentSuccess && countDownTimer != null) {
                    checkPaymentStatus();
                    handler.postDelayed(this, config.pollingInterval);
                }
            }
        }, 2000); // 首次延迟2秒，给用户时间扫码
    }
    
    private void checkPaymentStatus() {
        new Thread(() -> {
            try {
                // 调用真实的微信查询订单API
                boolean isPaid = weChatPayApi.queryOrderStatus(orderId);
                
                if (isPaid) {
                    handler.post(() -> {
                        onPaymentSuccess();
                    });
                }
                
            } catch (Exception e) {
                android.util.Log.w(TAG, "查询支付状态失败: " + e.getMessage());
                // 查询失败不影响继续轮询，但记录日志
            }
        }).start();
    }
    
    private void onPaymentSuccess() {
        isPaymentSuccess = true;
        
        // 停止倒计时
        if (countDownTimer != null) {
            countDownTimer.cancel();
        }
        
        // 更新UI
        tvPaymentStatus.setText("支付成功！正在打印...");
        tvCountdown.setText("00:00");
        
        // 返回打印结果
        Intent resultIntent = new Intent();
        resultIntent.putExtra("payment_success", true);
        setResult(RESULT_OK, resultIntent);
        
        Toast.makeText(this, "支付成功，开始打印", Toast.LENGTH_SHORT).show();
        
        // 延迟1秒后关闭支付页面
        handler.postDelayed(() -> {
            finish();
        }, 1000);
    }
    
    private void cancelPayment() {
        if (countDownTimer != null) {
            countDownTimer.cancel();
        }
        
        Intent resultIntent = new Intent();
        resultIntent.putExtra("payment_success", false);
        setResult(RESULT_CANCELED, resultIntent);
        
        finish();
    }
    
    @Override
    protected void onDestroy() {
        super.onDestroy();
        if (countDownTimer != null) {
            countDownTimer.cancel();
        }
    }
}
