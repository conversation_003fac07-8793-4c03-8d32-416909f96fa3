package com.jetson.aiartphoto;

import android.content.Intent;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.util.Log;

import com.jetson.aiartphoto.databinding.ActivityMainBinding;

public class MainActivity extends BaseActivity {

    private ActivityMainBinding binding;
    private Handler inactivityHandler;
    private static final long INACTIVITY_TIMEOUT = 120000; // 2分钟 = 120000毫秒

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        
        binding = ActivityMainBinding.inflate(getLayoutInflater());
        setContentView(binding.getRoot());
        
        Log.d("MainActivity", "显示静态背景图片");
        
        // 设置点击事件，点击屏幕进入拍照页面
        binding.fullScreenButton.setOnClickListener(v -> {
            startActivity(new Intent(MainActivity.this, CameraActivity.class));
        });
//        binding.fullScreenButton.setOnClickListener(v -> {
//            startActivity(new Intent(MainActivity.this, PrintActivity.class));
//        });
        
        // 初始化不活动计时器
        inactivityHandler = new Handler(Looper.getMainLooper());
    }
    
    @Override
    protected void onResume() {
        super.onResume();
        // 重置不活动计时器
        resetInactivityTimer();
    }
    
    @Override
    protected void onPause() {
        super.onPause();
        // 移除不活动计时器
        removeInactivityTimer();
    }
    
    @Override
    protected void onDestroy() {
        super.onDestroy();
        // 移除不活动计时器
        removeInactivityTimer();
    }
    
    private void resetInactivityTimer() {
        removeInactivityTimer();
        inactivityHandler.postDelayed(this::returnToSplashScreen, INACTIVITY_TIMEOUT);
    }
    
    private void removeInactivityTimer() {
        inactivityHandler.removeCallbacksAndMessages(null);
    }
    
    private void returnToSplashScreen() {
        // 如果当前已经在MainActivity，则不需要任何操作
        if (this instanceof MainActivity) {
            return;
        }
        
        // 否则，返回到MainActivity
        Intent intent = new Intent(this, MainActivity.class);
        intent.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP);
        startActivity(intent);
        finish();
    }
}