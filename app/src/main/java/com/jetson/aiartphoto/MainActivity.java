package com.jetson.aiartphoto;

import android.content.Intent;
import android.content.SharedPreferences;
import android.media.MediaPlayer;
import android.net.Uri;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.util.Log;
import android.view.View;
import android.widget.CompoundButton;
import android.widget.VideoView;

import com.jetson.aiartphoto.databinding.ActivityMainBinding;

public class MainActivity extends BaseActivity {

    private ActivityMainBinding binding;
    private Handler inactivityHandler;
    private static final long INACTIVITY_TIMEOUT = 120000; // 2分钟 = 120000毫秒

    private VideoView videoBackground;
    private SharedPreferences preferences;
    private static final String PREF_NAME = "screensaver_settings";
    private static final String KEY_VIDEO_MODE = "video_mode_enabled";
    private boolean isVideoMode = false;
    private boolean isSettingsPanelVisible = false;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        binding = ActivityMainBinding.inflate(getLayoutInflater());
        setContentView(binding.getRoot());

        // 初始化SharedPreferences
        preferences = getSharedPreferences(PREF_NAME, MODE_PRIVATE);
        isVideoMode = preferences.getBoolean(KEY_VIDEO_MODE, false);

        // 初始化视频背景
        videoBackground = binding.videoBackground;

        // 初始化屏保模式
        initScreensaverMode();

        Log.d("MainActivity", isVideoMode ? "显示视频背景" : "显示静态背景图片");

        // 设置点击事件，点击屏幕进入拍照页面
        binding.fullScreenButton.setOnClickListener(v -> {
            if (isSettingsPanelVisible) {
                hideSettingsPanel();
            } else {
                startActivity(new Intent(MainActivity.this, CameraActivity.class));
            }
        });

        // 长按显示设置面板
        binding.fullScreenButton.setOnLongClickListener(v -> {
            toggleSettingsPanel();
            return true;
        });

        // 设置屏保模式切换开关
        binding.screensaverModeSwitch.setChecked(isVideoMode);
        binding.screensaverModeSwitch.setOnCheckedChangeListener(new CompoundButton.OnCheckedChangeListener() {
            @Override
            public void onCheckedChanged(CompoundButton buttonView, boolean isChecked) {
                switchScreensaverMode(isChecked);
            }
        });

        // 初始化不活动计时器
        inactivityHandler = new Handler(Looper.getMainLooper());
    }
    
    @Override
    protected void onResume() {
        super.onResume();
        // 重置不活动计时器
        resetInactivityTimer();

        // 如果是视频模式，恢复视频播放
        if (isVideoMode && videoBackground != null) {
            if (!videoBackground.isPlaying()) {
                videoBackground.start();
            }
        }
    }

    @Override
    protected void onPause() {
        super.onPause();
        // 移除不活动计时器
        removeInactivityTimer();

        // 暂停视频播放
        if (isVideoMode && videoBackground != null && videoBackground.isPlaying()) {
            videoBackground.pause();
        }
    }
    
    @Override
    protected void onDestroy() {
        super.onDestroy();
        // 移除不活动计时器
        removeInactivityTimer();
    }
    
    private void resetInactivityTimer() {
        removeInactivityTimer();
        inactivityHandler.postDelayed(this::returnToSplashScreen, INACTIVITY_TIMEOUT);
    }
    
    private void removeInactivityTimer() {
        inactivityHandler.removeCallbacksAndMessages(null);
    }
    
    private void returnToSplashScreen() {
        // 如果当前已经在MainActivity，则不需要任何操作
        if (this instanceof MainActivity) {
            return;
        }

        // 否则，返回到MainActivity
        Intent intent = new Intent(this, MainActivity.class);
        intent.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP);
        startActivity(intent);
        finish();
    }

    private void initScreensaverMode() {
        if (isVideoMode) {
            showVideoBackground();
        } else {
            showImageBackground();
        }
    }

    private void switchScreensaverMode(boolean enableVideo) {
        isVideoMode = enableVideo;

        // 保存设置
        SharedPreferences.Editor editor = preferences.edit();
        editor.putBoolean(KEY_VIDEO_MODE, isVideoMode);
        editor.apply();

        // 切换背景
        if (isVideoMode) {
            showVideoBackground();
        } else {
            showImageBackground();
        }

        Log.d("MainActivity", "屏保模式切换为: " + (isVideoMode ? "视频" : "静态图片"));
    }

    private void showVideoBackground() {
        binding.fallbackBackground.setVisibility(View.GONE);
        binding.videoBackground.setVisibility(View.VISIBLE);

        // 设置视频资源
        Uri videoUri = Uri.parse("android.resource://" + getPackageName() + "/" + R.raw.bg2);
        videoBackground.setVideoURI(videoUri);

        // 设置视频播放监听器
        videoBackground.setOnPreparedListener(new MediaPlayer.OnPreparedListener() {
            @Override
            public void onPrepared(MediaPlayer mp) {
                mp.setLooping(true); // 循环播放
                mp.setVideoScalingMode(MediaPlayer.VIDEO_SCALING_MODE_SCALE_TO_FIT_WITH_CROPPING);
                videoBackground.start();
            }
        });

        // 设置错误监听器
        videoBackground.setOnErrorListener(new MediaPlayer.OnErrorListener() {
            @Override
            public boolean onError(MediaPlayer mp, int what, int extra) {
                Log.e("MainActivity", "视频播放错误，切换到静态背景");
                showImageBackground();
                return true;
            }
        });
    }

    private void showImageBackground() {
        binding.videoBackground.setVisibility(View.GONE);
        binding.fallbackBackground.setVisibility(View.VISIBLE);

        // 停止视频播放
        if (videoBackground.isPlaying()) {
            videoBackground.stopPlayback();
        }
    }

    private void toggleSettingsPanel() {
        if (isSettingsPanelVisible) {
            hideSettingsPanel();
        } else {
            showSettingsPanel();
        }
    }

    private void showSettingsPanel() {
        binding.screensaverModePanel.setVisibility(View.VISIBLE);
        isSettingsPanelVisible = true;

        // 3秒后自动隐藏
        inactivityHandler.postDelayed(this::hideSettingsPanel, 3000);
    }

    private void hideSettingsPanel() {
        binding.screensaverModePanel.setVisibility(View.GONE);
        isSettingsPanelVisible = false;
    }
}