package com.jetson.aiartphoto.api;

import android.content.Context;
import android.util.Base64;
import android.util.Log;

import com.jetson.aiartphoto.config.PaymentConfig;

import org.json.JSONException;
import org.json.JSONObject;

import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.security.KeyFactory;
import java.security.PrivateKey;
import java.security.SecureRandom;
import java.security.Signature;
import java.security.spec.PKCS8EncodedKeySpec;
import java.util.concurrent.TimeUnit;

import okhttp3.MediaType;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;

public class WeChatPayApi {
    private static final String TAG = "WeChatPayApi";
    private static final MediaType JSON = MediaType.get("application/json; charset=utf-8");
    
    private PaymentConfig config;
    private OkHttpClient client;
    private Context context;
    private PrivateKey privateKey;
    
    public WeChatPayApi(PaymentConfig config, Context context) {
        this.config = config;
        this.context = context;
        this.client = new OkHttpClient.Builder()
                .connectTimeout(config.apiTimeout, TimeUnit.MILLISECONDS)
                .readTimeout(config.apiTimeout, TimeUnit.MILLISECONDS)
                .writeTimeout(config.apiTimeout, TimeUnit.MILLISECONDS)
                .build();
        
        // 加载私钥
        loadPrivateKey();
    }
    
    private void loadPrivateKey() {
        try {
            InputStream is = context.getAssets().open("cert/apiclient_key.pem");
            byte[] buffer = new byte[is.available()];
            is.read(buffer);
            is.close();
            
            String privateKeyPEM = new String(buffer, StandardCharsets.UTF_8)
                    .replace("-----BEGIN PRIVATE KEY-----", "")
                    .replace("-----END PRIVATE KEY-----", "")
                    .replaceAll("\\s", "");
            
            byte[] decoded = Base64.decode(privateKeyPEM, Base64.DEFAULT);
            PKCS8EncodedKeySpec spec = new PKCS8EncodedKeySpec(decoded);
            KeyFactory keyFactory = KeyFactory.getInstance("RSA");
            privateKey = keyFactory.generatePrivate(spec);
            
            Log.d(TAG, "私钥加载成功");
            
        } catch (Exception e) {
            Log.e(TAG, "加载私钥失败", e);
        }
    }
    
    /**
     * 创建Native支付订单
     */
    public String createNativeOrder(String outTradeNo, String description, int totalAmount, String notifyUrl) throws Exception {
        String url = config.apiBaseUrl + config.nativePayPath;
        
        // 构建请求体
        JSONObject requestBody = new JSONObject();
        requestBody.put("appid", config.wechatAppId);
        requestBody.put("mchid", config.wechatMchId);
        requestBody.put("description", description);
        requestBody.put("out_trade_no", outTradeNo);
        requestBody.put("notify_url", notifyUrl);
        
        // 金额信息
        JSONObject amount = new JSONObject();
        amount.put("total", totalAmount);
        amount.put("currency", "CNY");
        requestBody.put("amount", amount);
        
        String requestBodyStr = requestBody.toString();
        Log.d(TAG, "统一下单请求: " + requestBodyStr);
        
        // 生成签名
        String authorization = generateAuthorization("POST", config.nativePayPath, requestBodyStr);
        
        // 创建请求
        Request request = new Request.Builder()
                .url(url)
                .post(RequestBody.create(requestBodyStr, JSON))
                .addHeader("Authorization", authorization)
                .addHeader("Accept", "application/json")
                .addHeader("Content-Type", "application/json")
                .addHeader("User-Agent", "AIArtPhoto/1.0")
                .build();
        
        // 发送请求
        try (Response response = client.newCall(request).execute()) {
            String responseBody = response.body().string();
            Log.d(TAG, "统一下单响应: " + responseBody);
            
            if (response.isSuccessful()) {
                JSONObject jsonResponse = new JSONObject(responseBody);
                return jsonResponse.getString("code_url");
            } else {
                throw new Exception("统一下单失败: " + responseBody);
            }
        }
    }
    
    /**
     * 查询订单状态
     */
    public boolean queryOrderStatus(String outTradeNo) throws Exception {
        String queryPath = config.queryOrderPath + "/" + outTradeNo + "?mchid=" + config.wechatMchId;
        String url = config.apiBaseUrl + queryPath;
        
        // 生成签名
        String authorization = generateAuthorization("GET", queryPath, "");
        
        // 创建请求
        Request request = new Request.Builder()
                .url(url)
                .get()
                .addHeader("Authorization", authorization)
                .addHeader("Accept", "application/json")
                .addHeader("User-Agent", "AIArtPhoto/1.0")
                .build();
        
        // 发送请求
        try (Response response = client.newCall(request).execute()) {
            String responseBody = response.body() != null ? response.body().string() : "";
            
            if (response.isSuccessful()) {
                Log.d(TAG, "查询订单成功: " + responseBody);
                JSONObject jsonResponse = new JSONObject(responseBody);
                String tradeState = jsonResponse.getString("trade_state");

                // 记录支付状态
                Log.d(TAG, "订单状态: " + tradeState);

                return "SUCCESS".equals(tradeState);
            } else {
                // 记录详细错误信息，但不抛异常，继续轮询
                Log.w(TAG, "查询订单失败 - HTTP " + response.code() + ": " + responseBody);

                // 如果是订单不存在等特定错误，可以解析错误码
                if (response.code() == 404) {
                    Log.w(TAG, "订单不存在，可能还未创建成功");
                }

                return false;
            }
        } catch (JSONException e) {
            Log.e(TAG, "解析查询响应失败", e);
            return false;
        }
    }
    
    /**
     * 生成微信支付APIv3签名
     */
    private String generateAuthorization(String method, String url, String body) {
        try {
            if (privateKey == null) {
                Log.e(TAG, "私钥未加载，无法生成签名");
                return "";
            }
            
            long timestamp = System.currentTimeMillis() / 1000;
            String nonceStr = generateNonceStr();
            
            // 构建签名串
            String signStr = method + "\n" + url + "\n" + timestamp + "\n" + nonceStr + "\n" + body + "\n";
            
            Log.d(TAG, "签名串: " + signStr);
            
            // 使用RSA私钥签名
            Signature signature = Signature.getInstance("SHA256withRSA");
            signature.initSign(privateKey);
            signature.update(signStr.getBytes(StandardCharsets.UTF_8));
            byte[] signBytes = signature.sign();
            String sign = Base64.encodeToString(signBytes, Base64.NO_WRAP);
            
            Log.d(TAG, "生成签名: " + sign);
            
            return String.format("WECHATPAY2-SHA256-RSA2048 mchid=\"%s\",nonce_str=\"%s\",signature=\"%s\",timestamp=\"%d\",serial_no=\"%s\"",
                    config.wechatMchId, nonceStr, sign, timestamp, config.wechatCertSerialNo);
                    
        } catch (Exception e) {
            Log.e(TAG, "生成签名失败", e);
            return "";
        }
    }
    
    private String generateNonceStr() {
        String chars = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
        SecureRandom random = new SecureRandom();
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < 32; i++) {
            sb.append(chars.charAt(random.nextInt(chars.length())));
        }
        return sb.toString();
    }
}
