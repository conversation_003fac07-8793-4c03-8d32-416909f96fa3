package com.jetson.aiartphoto.config;

import android.content.Context;
import android.util.Log;

import org.json.JSONException;
import org.json.JSONObject;

import java.io.IOException;
import java.io.InputStream;

public class PaymentConfig {
    private static final String TAG = "PaymentConfig";
    private static final String CONFIG_FILE = "payment_config.json";
    
    private static PaymentConfig instance;
    private JSONObject config;
    
    // 微信支付配置
    public String wechatAppId;
    public String wechatMchId;
    public String wechatApiV3Key;
    public String wechatCertSerialNo;
    public String wechatPrivateKeyPath;
    public String wechatNotifyUrl;
    
    // API配置
    public String apiBaseUrl;
    public String nativePayPath;
    public String queryOrderPath;
    public int apiTimeout;
    
    // 业务配置
    public String productName;
    public String productDesc;
    public int pricePerImage; // 分为单位
    public long paymentTimeout;
    public long pollingInterval;
    
    private PaymentConfig() {}
    
    public static synchronized PaymentConfig getInstance(Context context) {
        if (instance == null) {
            instance = new PaymentConfig();
            instance.loadConfig(context);
        }
        return instance;
    }
    
    private void loadConfig(Context context) {
        try {
            InputStream is = context.getAssets().open(CONFIG_FILE);
            byte[] buffer = new byte[is.available()];
            is.read(buffer);
            is.close();
            
            String json = new String(buffer, "UTF-8");
            config = new JSONObject(json);
            
            parseConfig();
            
        } catch (IOException | JSONException e) {
            Log.e(TAG, "加载配置文件失败", e);
            setDefaultConfig();
        }
    }
    
    private void parseConfig() throws JSONException {
        // 微信支付配置
        JSONObject wechat = config.getJSONObject("wechat");
        wechatAppId = wechat.getString("app_id");
        wechatMchId = wechat.getString("mch_id");
        wechatApiV3Key = wechat.getString("api_v3_key");
        wechatCertSerialNo = wechat.getString("cert_serial_no");
        wechatPrivateKeyPath = wechat.getString("private_key_path");
        wechatNotifyUrl = wechat.getString("notify_url");
        
        // API配置
        JSONObject api = config.getJSONObject("api");
        apiBaseUrl = api.getString("base_url");
        nativePayPath = api.getString("native_pay");
        queryOrderPath = api.getString("query_order");
        apiTimeout = api.getInt("timeout");
        
        // 业务配置
        JSONObject business = config.getJSONObject("business");
        productName = business.getString("product_name");
        productDesc = business.getString("product_desc");
        pricePerImage = business.getInt("price_per_image");
        paymentTimeout = business.getLong("payment_timeout");
        pollingInterval = business.getLong("polling_interval");
    }
    
    private void setDefaultConfig() {
        // 设置默认值
        wechatAppId = "wxe3c6cf1a3636e81f";
        productName = "AI艺术照片打印";
        productDesc = "AI换脸艺术照片打印服务";
        pricePerImage = 1; // 1分
        paymentTimeout = 300000; // 5分钟
        pollingInterval = 3000; // 3秒
        apiTimeout = 30000; // 30秒
        apiBaseUrl = "https://api.mch.weixin.qq.com";
        nativePayPath = "/v3/pay/transactions/native";
        queryOrderPath = "/v3/pay/transactions/out-trade-no";
    }
    
    public boolean isConfigValid() {
        return wechatAppId != null && wechatMchId != null && 
               wechatApiV3Key != null && wechatCertSerialNo != null;
    }
}