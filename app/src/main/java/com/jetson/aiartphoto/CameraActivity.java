package com.jetson.aiartphoto;

import android.Manifest;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.os.Bundle;
import android.os.CountDownTimer;
import android.os.Handler;
import android.os.Looper;
import android.util.Log;
import android.view.View;
import android.widget.ImageView;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.camera.core.CameraSelector;
import androidx.camera.core.ImageCapture;
import androidx.camera.core.ImageCaptureException;
import androidx.camera.core.ImageProxy;
import androidx.camera.core.Preview;
import androidx.camera.lifecycle.ProcessCameraProvider;
import androidx.core.app.ActivityCompat;
import androidx.core.content.ContextCompat;

import com.google.common.util.concurrent.ListenableFuture;
import com.jetson.aiartphoto.databinding.ActivityCameraBinding;
import com.jetson.aiartphoto.utils.BitmapUtils;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.nio.ByteBuffer;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

public class CameraActivity extends BaseActivity {
    private static final String TAG = "CameraActivity";
    private static final int REQUEST_CODE_PERMISSIONS = 10;
    private static final String[] REQUIRED_PERMISSIONS = new String[]{Manifest.permission.CAMERA};
    private static final long INACTIVITY_TIMEOUT = 120000; // 2分钟 = 120000毫秒

    private ActivityCameraBinding binding;
    private ImageCapture imageCapture;
    private ExecutorService cameraExecutor;
    private File photoFile;
    private Handler inactivityHandler;
    private String templateUrl;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        
        // 锁定屏幕方向为竖屏
        setRequestedOrientation(android.content.pm.ActivityInfo.SCREEN_ORIENTATION_PORTRAIT);
        
        binding = ActivityCameraBinding.inflate(getLayoutInflater());
        setContentView(binding.getRoot());

        // 不再校验template_url参数，保证拍照页始终可进入

        // 检查权限
        if (allPermissionsGranted()) {
            startCamera();
        } else {
            ActivityCompat.requestPermissions(this, REQUIRED_PERMISSIONS, REQUEST_CODE_PERMISSIONS);
        }

        // 设置拍照按钮点击事件
        binding.cameraCaptureButton.setOnClickListener(v -> {
            startCountdown();
        });

        // 设置重拍按钮点击事件
        binding.retakeButton.setOnClickListener(v -> {
            binding.photoPreviewContainer.setVisibility(View.GONE);
            binding.cameraCaptureButton.setEnabled(true);
            resetInactivityTimer();
        });

        // 设置确认按钮点击事件
        binding.confirmButton.setOnClickListener(v -> {
            if (photoFile != null && photoFile.exists()) {
                // 跳转到风格选择页面
                Intent intent = new Intent(CameraActivity.this, StyleSelectionActivity.class);
                intent.putExtra("photo_path", photoFile.getAbsolutePath());
                startActivity(intent);
                finish();
            } else {
                Toast.makeText(this, "照片保存失败，请重试", Toast.LENGTH_SHORT).show();
            }
        });

        cameraExecutor = Executors.newSingleThreadExecutor();
        
        // 初始化不活动计时器
        inactivityHandler = new Handler(Looper.getMainLooper());
    }

    private void startCountdown() {
        binding.cameraCaptureButton.setEnabled(false);
        binding.countdownText.setVisibility(View.VISIBLE);

        new CountDownTimer(3000, 1000) {
            @Override
            public void onTick(long millisUntilFinished) {
                int secondsRemaining = (int) (millisUntilFinished / 1000) + 1;
                binding.countdownText.setText(String.valueOf(secondsRemaining));
            }

            @Override
            public void onFinish() {
                binding.countdownText.setVisibility(View.INVISIBLE);
                takePhoto();
            }
        }.start();
    }

    private void takePhoto() {
        if (imageCapture == null) {
            Toast.makeText(this, "相机未准备好", Toast.LENGTH_SHORT).show();
            binding.cameraCaptureButton.setEnabled(true);
            return;
        }
        
        Log.d(TAG, "开始拍照...");
        
        // 显示加载提示（可选）
        // binding.loadingIndicator.setVisibility(View.VISIBLE);

        imageCapture.takePicture(ContextCompat.getMainExecutor(this), new ImageCapture.OnImageCapturedCallback() {
            @Override
            public void onCaptureSuccess(@NonNull ImageProxy image) {
                Log.d(TAG, "拍照成功，开始处理图像...");
                // 获取图像旋转度数
                final int rotationDegrees = image.getImageInfo().getRotationDegrees();
                
                // 在后台线程处理图像
                cameraExecutor.execute(() -> {
                    Log.d(TAG, "开始在后台线程处理图像...");
                    // 将图像转换为位图
                    Bitmap bitmap = imageProxyToBitmap(image);
                    image.close();

                    if (bitmap != null) {
                        Log.d(TAG, "图像转换成功，大小: " + bitmap.getWidth() + "x" + bitmap.getHeight());
                        // 根据需要旋转位图
                        bitmap = BitmapUtils.rotateBitmapIfNeeded(bitmap, rotationDegrees);
                        Log.d(TAG, "图像旋转完成，开始保存...");
                        
                        // 保存照片到文件
                        photoFile = saveImageToFile(bitmap);
                        
                        // 在主线程更新UI
                        final Bitmap finalBitmap = bitmap;
                        runOnUiThread(() -> {
                            if (photoFile != null && photoFile.exists()) {
                                Log.d(TAG, "照片保存成功: " + photoFile.getAbsolutePath());
                                // 显示预览，确保方向正确
                                binding.photoPreview.setScaleType(ImageView.ScaleType.FIT_CENTER);
                                binding.photoPreview.setImageBitmap(finalBitmap);
                                binding.photoPreviewContainer.setVisibility(View.VISIBLE);
                                // 拍照后不再自动跳转风格选择页，等待用户点击“确认”
                                binding.cameraCaptureButton.setEnabled(true);
                            } else {
                                Log.e(TAG, "照片保存失败");
                                Toast.makeText(CameraActivity.this, "照片保存失败", Toast.LENGTH_SHORT).show();
                                binding.cameraCaptureButton.setEnabled(true);
                            }
                        });
                    } else {
                        Log.e(TAG, "图像转换失败，无法获取位图");
                        runOnUiThread(() -> {
                            Toast.makeText(CameraActivity.this, "照片处理失败", Toast.LENGTH_SHORT).show();
                            binding.cameraCaptureButton.setEnabled(true);
                        });
                    }
                });
            }

            @Override
            public void onError(@NonNull ImageCaptureException exception) {
                Log.e(TAG, "拍照失败: " + exception.getMessage(), exception);
                Toast.makeText(CameraActivity.this, "拍照失败: " + exception.getMessage(), Toast.LENGTH_SHORT).show();
                binding.cameraCaptureButton.setEnabled(true);
            }
        });
    }

    private Bitmap imageProxyToBitmap(ImageProxy image) {
        try {
            Log.d(TAG, "开始转换ImageProxy为Bitmap...");
            ByteBuffer buffer = image.getPlanes()[0].getBuffer();
            byte[] bytes = new byte[buffer.remaining()];
            buffer.get(bytes);
            
            // 创建BitmapFactory选项以控制内存使用
            BitmapFactory.Options options = new BitmapFactory.Options();
            options.inMutable = true; // 使位图可变，以便后续操作可以重用它
            
            // 对于大分辨率图片，可以考虑添加采样选项降低内存使用
            // options.inSampleSize = 2; // 将图像尺寸缩小为原来的1/2
            
            Bitmap bitmap = BitmapFactory.decodeByteArray(bytes, 0, bytes.length, options);
            Log.d(TAG, "ImageProxy转换完成，大小: " + (bitmap != null ? bitmap.getWidth() + "x" + bitmap.getHeight() : "null"));
            return bitmap;
        } catch (Exception e) {
            Log.e(TAG, "转换ImageProxy为Bitmap失败: " + e.getMessage(), e);
            return null;
        }
    }

    private File saveImageToFile(Bitmap bitmap) {
        if (bitmap == null) {
            Log.e(TAG, "无法保存图像：位图为null");
            return null;
        }
        
        Log.d(TAG, "开始保存图像到文件...");
        File outputDir = new File(getExternalFilesDir(null), "photos");
        if (!outputDir.exists()) {
            boolean created = outputDir.mkdirs();
            if (!created) {
                Log.e(TAG, "创建输出目录失败: " + outputDir.getAbsolutePath());
                return null;
            }
        }

        File outputFile = new File(outputDir, "photo_" + System.currentTimeMillis() + ".jpg");
        FileOutputStream fos = null;
        try {
            fos = new FileOutputStream(outputFile);
            boolean success = bitmap.compress(Bitmap.CompressFormat.JPEG, 95, fos); // 降低质量到95%以减少文件大小
            fos.flush();
            
            if (!success) {
                Log.e(TAG, "压缩图像失败");
                return null;
            }
            
            // 确保EXIF方向信息正确
            try {
                BitmapUtils.saveOrientationToExif(outputFile.getAbsolutePath(), 0);
            } catch (Exception e) {
                Log.e(TAG, "保存EXIF信息失败: " + e.getMessage(), e);
                // 继续执行，因为这不是致命错误
            }
            
            Log.d(TAG, "图像保存成功: " + outputFile.getAbsolutePath() + ", 大小: " + outputFile.length() + " 字节");
            return outputFile;
        } catch (IOException e) {
            Log.e(TAG, "保存照片失败: " + e.getMessage(), e);
            if (outputFile.exists()) {
                boolean deleted = outputFile.delete();
                if (!deleted) {
                    Log.w(TAG, "无法删除失败的输出文件: " + outputFile.getAbsolutePath());
                }
            }
            return null;
        } finally {
            if (fos != null) {
                try {
                    fos.close();
                } catch (IOException e) {
                    Log.e(TAG, "关闭文件输出流失败: " + e.getMessage(), e);
                }
            }
        }
    }

    private void startCamera() {
        Log.d(TAG, "开始初始化相机...");
        ListenableFuture<ProcessCameraProvider> cameraProviderFuture = ProcessCameraProvider.getInstance(this);

        cameraProviderFuture.addListener(() -> {
            try {
                ProcessCameraProvider cameraProvider = cameraProviderFuture.get();
                Log.d(TAG, "成功获取相机提供者");

                // 设置预览
                Preview preview = new Preview.Builder()
                        .build();
                preview.setSurfaceProvider(binding.viewFinder.getSurfaceProvider());
                Log.d(TAG, "预览设置完成");

                // 设置图像捕获
                imageCapture = new ImageCapture.Builder()
                        .setCaptureMode(ImageCapture.CAPTURE_MODE_MAXIMIZE_QUALITY)
                        .setTargetRotation(getWindowManager().getDefaultDisplay().getRotation())
                        .build();
                Log.d(TAG, "图像捕获设置完成");

                // 直接使用CameraX提供的相机选择器，避免手动处理相机ID
                CameraSelector cameraSelector;
                
                try {
                    // 尝试使用后置相机
                    Log.d(TAG, "尝试使用后置相机");
                    cameraSelector = CameraSelector.DEFAULT_BACK_CAMERA;
                    
                    // 绑定生命周期前先解绑所有用例
                    cameraProvider.unbindAll();
                    Log.d(TAG, "已解绑所有相机用例");
                    
                    // 绑定生命周期
                    cameraProvider.bindToLifecycle(this, cameraSelector, preview, imageCapture);
                    Log.d(TAG, "后置摄像头绑定成功");
                } catch (Exception e) {
                    Log.e(TAG, "使用后置相机失败: " + e.getMessage(), e);
                    
                    try {
                        // 如果后置相机失败，尝试使用前置相机
                        Log.d(TAG, "尝试使用前置相机");
                        cameraSelector = CameraSelector.DEFAULT_FRONT_CAMERA;
                        
                        // 重新绑定
                        cameraProvider.unbindAll();
                        cameraProvider.bindToLifecycle(this, cameraSelector, preview, imageCapture);
                        Log.d(TAG, "前置摄像头绑定成功");
                    } catch (Exception e2) {
                        Log.e(TAG, "使用前置相机失败: " + e2.getMessage(), e2);
                        
                        try {
                            // 最后尝试使用默认相机选择器
                            Log.d(TAG, "尝试使用默认相机选择器");
                            cameraSelector = new CameraSelector.Builder().build();
                            
                            // 重新绑定
                            cameraProvider.unbindAll();
                            cameraProvider.bindToLifecycle(this, cameraSelector, preview, imageCapture);
                            Log.d(TAG, "默认相机绑定成功");
                        } catch (Exception e3) {
                            Log.e(TAG, "所有相机尝试均失败: " + e3.getMessage(), e3);
                            Toast.makeText(this, "无法启动相机，请检查设备", Toast.LENGTH_LONG).show();
                            finish();
                        }
                    }
                }
            } catch (ExecutionException | InterruptedException e) {
                Log.e(TAG, "相机启动失败: " + e.getMessage(), e);
                Toast.makeText(this, "相机启动失败: " + e.getMessage(), Toast.LENGTH_SHORT).show();
                finish();
            }
        }, ContextCompat.getMainExecutor(this));
    }

    private boolean allPermissionsGranted() {
        for (String permission : REQUIRED_PERMISSIONS) {
            if (ContextCompat.checkSelfPermission(this, permission) != PackageManager.PERMISSION_GRANTED) {
                return false;
            }
        }
        return true;
    }

    @Override
    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions, @NonNull int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
        if (requestCode == REQUEST_CODE_PERMISSIONS) {
            if (allPermissionsGranted()) {
                startCamera();
            } else {
                Toast.makeText(this, "需要相机权限才能使用此功能", Toast.LENGTH_SHORT).show();
                finish();
            }
        }
    }

    @Override
    protected void onResume() {
        super.onResume();
        resetInactivityTimer();
    }

    @Override
    protected void onPause() {
        super.onPause();
        removeInactivityTimer();
    }

    @Override
    protected void onDestroy() {
        Log.d(TAG, "CameraActivity onDestroy");
        super.onDestroy();
        
        // 释放相机资源
        try {
            ProcessCameraProvider cameraProvider = ProcessCameraProvider.getInstance(this).get();
            if (cameraProvider != null) {
                cameraProvider.unbindAll();
                Log.d(TAG, "相机资源已解绑");
            }
        } catch (Exception e) {
            Log.e(TAG, "释放相机资源失败: " + e.getMessage(), e);
        }
        
        // 关闭线程池
        if (cameraExecutor != null && !cameraExecutor.isShutdown()) {
            cameraExecutor.shutdown();
            Log.d(TAG, "相机执行器已关闭");
        }
        
        // 移除不活动计时器
        removeInactivityTimer();
    }
    
    private void resetInactivityTimer() {
        removeInactivityTimer();
        inactivityHandler.postDelayed(this::returnToSplashScreen, INACTIVITY_TIMEOUT);
    }
    
    private void removeInactivityTimer() {
        if (inactivityHandler != null) {
            inactivityHandler.removeCallbacksAndMessages(null);
        }
    }
    
    private void returnToSplashScreen() {
        Intent intent = new Intent(this, MainActivity.class);
        intent.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP);
        startActivity(intent);
        finish();
    }
} 