package com.jetson.aiartphoto;

import android.content.Intent;
import android.graphics.BitmapFactory;
import android.net.Uri;
import android.os.Bundle;
import android.os.Debug;
import android.view.View;
import android.widget.Button;
import android.widget.ImageButton;
import android.widget.ImageView;
import android.widget.TextView;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.bumptech.glide.Glide;
import com.hiti.usb.service.ServiceConnector;
import com.jetson.aiartphoto.PrinterOperation;
import com.hiti.usb.printer.PrinterJob;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;

import java.util.ArrayList;
import java.util.List;
import android.view.ViewGroup;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;

import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;
import java.io.FileOutputStream;
import android.graphics.Bitmap;
import android.graphics.Matrix;
import android.media.ExifInterface;

public class PrintActivity extends BaseActivity {
    private static final Log log = LogFactory.getLog(PrintActivity.class);
    private static final int PAYMENT_REQUEST_CODE = 1001;
    private ServiceConnector serviceConnector;
    private PrinterOperation printerOperation;
    private String m_strTablesRoot;
    private List<String> urlList = new ArrayList<>();
    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_print);
        // 注册USB服务
        serviceConnector = ServiceConnector.register(this, null);
        // 新增：启动服务，防止“service is not start”报错
        serviceConnector.StartService();

        //android.util.Log.d("12333333", "getHitiServiceStatus: " + serviceConnector.getHitiServiceStatus());

        // 拷贝色表（只需一次）
        m_strTablesRoot = this.getExternalFilesDir(null).getAbsolutePath() + "/Tables";
        copyFileOrDir("Tables");
        // 初始化打印操作
        printerOperation = new PrinterOperation(this, serviceConnector);
        printerOperation.m_strTablesRoot = m_strTablesRoot;

        ImageButton homeButton = findViewById(R.id.homeButton);
        homeButton.setOnClickListener(v -> {
            Intent intent = new Intent(this, MainActivity.class);
            intent.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP);
            startActivity(intent);
            finish();
        });
        ImageButton backButton = findViewById(R.id.backButton);
        backButton.setOnClickListener(v -> finish());
        RecyclerView recyclerView = findViewById(R.id.printImageList);
        recyclerView.setLayoutManager(new androidx.recyclerview.widget.GridLayoutManager(this, 1));
        Intent intent = getIntent();
        String[] urls = intent.getStringArrayExtra("selected_urls");
        urlList.clear();
        if (urls != null) {
            for (String url : urls) {
                urlList.add(url);
            }
        }
        if (urlList.isEmpty()) {
            Toast.makeText(this, "未选择任何图片", Toast.LENGTH_SHORT).show();
            finish();
            return;
        }
        PrintImageAdapter adapter = new PrintImageAdapter(urlList);
        recyclerView.setAdapter(adapter);
        ImageButton printButton = findViewById(R.id.printButton);
        printButton.setOnClickListener(v -> {
            // 先弹出支付弹窗
            Intent paymentIntent = new Intent(this, PaymentActivity.class);
            paymentIntent.putExtra("image_count", urlList.size());
            startActivityForResult(paymentIntent, PAYMENT_REQUEST_CODE);
        });
    }
    @Override
    protected void onDestroy() {
        super.onDestroy();
        if (serviceConnector != null) {
            serviceConnector.unregister();
        }
    }
    
    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        
        if (requestCode == PAYMENT_REQUEST_CODE) {
            if (resultCode == RESULT_OK && data != null) {
                boolean paymentSuccess = data.getBooleanExtra("payment_success", false);
                if (paymentSuccess) {
                    // 支付成功，开始打印
                    printSelectedImages();
                } else {
                    // 支付失败或取消
                    Toast.makeText(this, "支付已取消", Toast.LENGTH_SHORT).show();
                }
            } else {
                // 支付失败或取消
                Toast.makeText(this, "支付已取消", Toast.LENGTH_SHORT).show();
            }
        }
    }
    // 打印所有选中图片
    private void printSelectedImages() {
        // 新增：打印前判断服务状态
        String status = null;
        try {
            status = serviceConnector.getHitiServiceStatus();
        } catch (Exception e) {
            e.printStackTrace();
        }
        if (status == null || !status.toLowerCase().contains("running")) {
            runOnUiThread(() -> Toast.makeText(PrintActivity.this, "打印服务未启动，请稍后重试", Toast.LENGTH_SHORT).show());
            return;
        }
        if (urlList.isEmpty()) {
            Toast.makeText(this, "没有可打印的图片", Toast.LENGTH_SHORT).show();
            return;
        }
        new Thread(() -> {
            boolean allSuccess = true;
            OkHttpClient client = new OkHttpClient();
            for (String path : urlList) {
                String realPath = path;
                // 只支持本地文件路径，如果是网络图片，先下载到本地
                if (path.startsWith("http")) {
                    realPath = downloadImageToLocal(path, client);
                    if (realPath == null) {
                        final String pathForToast = path;
                        runOnUiThread(() -> Toast.makeText(PrintActivity.this, "图片下载失败: " + pathForToast, Toast.LENGTH_SHORT).show());
                        allSuccess = false;
                        continue;
                    }
                } else if (path.startsWith("file://")) {
                    realPath = path.replace("file://", "");
                }
                File file = new File(realPath);
                if (!file.exists()) {
                    final String pathForToast = realPath;
                    runOnUiThread(() -> Toast.makeText(PrintActivity.this, "图片不存在: " + pathForToast, Toast.LENGTH_SHORT).show());
                    allSuccess = false;
                    continue;
                }
                // 新增：设置打印参数，确保4x6纸张
                printerOperation.PaperType = 2;
                printerOperation.MATTE = 1;
                printerOperation.PRINTCOUNT = 1;
                printerOperation.PRINTMODE = 0;
                // 新增：自动resize到1844x1240
                String printPath = ensureImageSize1844x1240(realPath);
                PrinterJob job = printerOperation.print(printPath);
                if (job == null || job.errCode.value != 0) {
                    final String errMsg = (job != null ? job.errCode.description : "未知错误");
                    runOnUiThread(() -> Toast.makeText(PrintActivity.this, "打印失败: " + errMsg, Toast.LENGTH_LONG).show());
                    allSuccess = false;
                }
            }
            if (allSuccess) {
                runOnUiThread(() -> Toast.makeText(PrintActivity.this, "所有图片已发送打印", Toast.LENGTH_SHORT).show());
            }
        }).start();
    }

    // 下载网络图片到本地临时文件，返回本地路径
    private String downloadImageToLocal(String url, OkHttpClient client) {
        try {
            Request request = new Request.Builder().url(url).build();
            Response response = client.newCall(request).execute();
            if (response.isSuccessful() && response.body() != null) {
                File targetFile = new File(getExternalFilesDir(null), "print_" + System.currentTimeMillis() + ".jpg");
                try (FileOutputStream fos = new FileOutputStream(targetFile)) {
                    fos.write(response.body().bytes());
                    fos.flush();
                }
                return targetFile.getAbsolutePath();
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }
    // 新增：将图片resize到4x6标准分辨率，并自动修正EXIF方向，强制横向
    private String ensureImageSize1844x1240(String path) {
        try {
            Bitmap bitmap = BitmapFactory.decodeFile(path);
            if (bitmap == null) return path;
            // 读取EXIF方向并自动旋转
            int rotate = 0;
            try {
                ExifInterface exif = new ExifInterface(path);
                int orientation = exif.getAttributeInt(ExifInterface.TAG_ORIENTATION, ExifInterface.ORIENTATION_NORMAL);
                switch (orientation) {
                    case ExifInterface.ORIENTATION_ROTATE_90:
                        rotate = 90; break;
                    case ExifInterface.ORIENTATION_ROTATE_180:
                        rotate = 180; break;
                    case ExifInterface.ORIENTATION_ROTATE_270:
                        rotate = 270; break;
                }
            } catch (Exception e) { /* ignore */ }
            if (rotate != 0) {
                Matrix matrix = new Matrix();
                matrix.postRotate(rotate);
                Bitmap rotated = Bitmap.createBitmap(bitmap, 0, 0, bitmap.getWidth(), bitmap.getHeight(), matrix, true);
                bitmap.recycle();
                bitmap = rotated;
            }
            // 如果修正后仍为竖图，强制再旋转90°
            if (bitmap.getWidth() < bitmap.getHeight()) {
                Matrix matrix = new Matrix();
                matrix.postRotate(90);
                Bitmap rotated = Bitmap.createBitmap(bitmap, 0, 0, bitmap.getWidth(), bitmap.getHeight(), matrix, true);
                bitmap.recycle();
                bitmap = rotated;
            }
            if (bitmap.getWidth() == 1844 && bitmap.getHeight() == 1240) {
                return path;
            }
            // 按比例缩放并居中裁剪到1844x1240
            float scale = Math.max(1844f / bitmap.getWidth(), 1240f / bitmap.getHeight());
            int newW = Math.round(bitmap.getWidth() * scale);
            int newH = Math.round(bitmap.getHeight() * scale);
            Bitmap scaled = Bitmap.createScaledBitmap(bitmap, newW, newH, true);
            Bitmap cropped = Bitmap.createBitmap(scaled, (newW - 1844) / 2, (newH - 1240) / 2, 1844, 1240);
            File outFile = new File(getExternalFilesDir(null), "print_resized_" + System.currentTimeMillis() + ".jpg");
            try (FileOutputStream fos = new FileOutputStream(outFile)) {
                cropped.compress(Bitmap.CompressFormat.JPEG, 95, fos);
            }
            bitmap.recycle();
            scaled.recycle();
            cropped.recycle();
            return outFile.getAbsolutePath();
        } catch (Exception e) {
            e.printStackTrace();
            return path;
        }
    }
    // 色表拷贝方法（参考demo）
    private void copyFileOrDir(String path) {
        try {
            String[] assets = getAssets().list(path);
            if (assets == null || assets.length == 0) {
                copyFile(path);
            } else {
                String fullPath = getExternalFilesDir(null) + "/" + path;
                File dir = new File(fullPath);
                if (!dir.exists()) dir.mkdir();
                for (String asset : assets) {
                    copyFileOrDir(path + "/" + asset);
                }
            }
        } catch (IOException ex) {
            ex.printStackTrace();
        }
    }
    private void copyFile(String filename) {
        try (InputStream in = getAssets().open(filename)) {
            String newFileName = getExternalFilesDir(null) + "/" + filename;
            File outFile = new File(newFileName);
            if (outFile.exists()) return;
            try (OutputStream out = new java.io.FileOutputStream(newFileName)) {
                byte[] buffer = new byte[1024];
                int read;
                while ((read = in.read(buffer)) != -1) {
                    out.write(buffer, 0, read);
                }
                out.flush();
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    static class PrintImageAdapter extends RecyclerView.Adapter<PrintImageAdapter.ViewHolder> {
        private final List<String> urls;
        PrintImageAdapter(List<String> urls) {
            this.urls = urls;
        }
        @NonNull
        @Override
        public ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
            View view = View.inflate(parent.getContext(), R.layout.item_print_image, null);
            return new ViewHolder(view);
        }
        @Override
        public void onBindViewHolder(@NonNull ViewHolder holder, int position) {
            String url = urls.get(position);
            if (url.startsWith("file:///android_asset/")) {
                // 加载assets图片
                String assetPath = url.replace("file:///android_asset/", "");
                try {
                    holder.printImage.setImageBitmap(BitmapFactory.decodeStream(holder.printImage.getContext().getAssets().open(assetPath)));
                } catch (Exception e) {
                    holder.printImage.setImageResource(android.R.drawable.ic_menu_report_image);
                }
            } else if (url.startsWith("http")) {
                Glide.with(holder.printImage.getContext())
                        .load(url)
                        .placeholder(android.R.drawable.ic_menu_gallery)
                        .into(holder.printImage);
            } else {
                Glide.with(holder.printImage.getContext())
                        .load(Uri.fromFile(new java.io.File(url)))
                        .placeholder(android.R.drawable.ic_menu_gallery)
                        .into(holder.printImage);
            }
        }
        @Override
        public int getItemCount() {
            return urls.size();
        }
        static class ViewHolder extends RecyclerView.ViewHolder {
            ImageView printImage;
            ViewHolder(@NonNull View itemView) {
                super(itemView);
                printImage = itemView.findViewById(R.id.printImage);
            }
        }
    }
} 