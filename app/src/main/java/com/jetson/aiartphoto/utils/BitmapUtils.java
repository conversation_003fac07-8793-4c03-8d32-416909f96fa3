package com.jetson.aiartphoto.utils;

import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.graphics.Matrix;
import android.media.ExifInterface;
import android.util.Base64;
import android.util.Log;

import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;

public class BitmapUtils {
    private static final String TAG = "BitmapUtils";
    
    /**
     * 调整位图以确保其方向正确
     * @param bitmap 要调整的位图
     * @param rotationDegrees 旋转角度
     * @return 调整后的位图
     */
    public static Bitmap rotateBitmapIfNeeded(Bitmap bitmap, int rotationDegrees) {
        if (rotationDegrees == 0) {
            return bitmap;
        }
        
        Matrix matrix = new Matrix();
        matrix.postRotate(rotationDegrees);
        
        // 创建新的位图
        return Bitmap.createBitmap(bitmap, 0, 0, bitmap.getWidth(), bitmap.getHeight(), matrix, true);
    }
    
    /**
     * 根据Exif信息旋转位图
     * @param bitmap 原始位图
     * @param imagePath 图像文件路径
     * @return 旋转后的位图
     */
    public static Bitmap rotateBitmapByExif(Bitmap bitmap, String imagePath) {
        try {
            ExifInterface exif = new ExifInterface(imagePath);
            int orientation = exif.getAttributeInt(ExifInterface.TAG_ORIENTATION, ExifInterface.ORIENTATION_NORMAL);
            
            Matrix matrix = new Matrix();
            switch (orientation) {
                case ExifInterface.ORIENTATION_ROTATE_90:
                    matrix.postRotate(90);
                    break;
                case ExifInterface.ORIENTATION_ROTATE_180:
                    matrix.postRotate(180);
                    break;
                case ExifInterface.ORIENTATION_ROTATE_270:
                    matrix.postRotate(270);
                    break;
                default:
                    return bitmap;
            }
            
            return Bitmap.createBitmap(bitmap, 0, 0, bitmap.getWidth(), bitmap.getHeight(), matrix, true);
        } catch (IOException e) {
            Log.e(TAG, "读取Exif信息失败: " + e.getMessage(), e);
            return bitmap;
        }
    }
    
    /**
     * 保存图像方向信息到Exif
     * @param imagePath 图像文件路径
     * @param orientation 方向值
     */
    public static void saveOrientationToExif(String imagePath, int orientation) {
        try {
            ExifInterface exif = new ExifInterface(imagePath);
            
            switch (orientation) {
                case 90:
                    exif.setAttribute(ExifInterface.TAG_ORIENTATION, String.valueOf(ExifInterface.ORIENTATION_ROTATE_90));
                    break;
                case 180:
                    exif.setAttribute(ExifInterface.TAG_ORIENTATION, String.valueOf(ExifInterface.ORIENTATION_ROTATE_180));
                    break;
                case 270:
                    exif.setAttribute(ExifInterface.TAG_ORIENTATION, String.valueOf(ExifInterface.ORIENTATION_ROTATE_270));
                    break;
                default:
                    exif.setAttribute(ExifInterface.TAG_ORIENTATION, String.valueOf(ExifInterface.ORIENTATION_NORMAL));
                    break;
            }
            
            exif.saveAttributes();
        } catch (IOException e) {
            Log.e(TAG, "保存Exif信息失败: " + e.getMessage(), e);
        }
    }

    /**
     * 将图片文件转换为Base64编码的字符串
     * @param imagePath 图片文件路径
     * @return Base64编码的字符串
     */
    public static String convertImageToBase64(String imagePath) {
        try {
            File imageFile = new File(imagePath);
            if (!imageFile.exists()) {
                return null;
            }
            
            // 读取图片文件
            InputStream inputStream = new FileInputStream(imageFile);
            byte[] bytes = new byte[(int) imageFile.length()];
            inputStream.read(bytes);
            inputStream.close();
            
            // 转换为Base64
            return Base64.encodeToString(bytes, Base64.NO_WRAP);
        } catch (IOException e) {
            Log.e(TAG, "转换图片为Base64失败: " + e.getMessage(), e);
            return null;
        }
    }
    
    /**
     * 将Bitmap转换为Base64编码的字符串
     * @param bitmap 位图
     * @param quality 压缩质量 (0-100)
     * @return Base64编码的字符串
     */
    public static String convertBitmapToBase64(Bitmap bitmap, int quality) {
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        bitmap.compress(Bitmap.CompressFormat.JPEG, quality, outputStream);
        byte[] bytes = outputStream.toByteArray();
        return Base64.encodeToString(bytes, Base64.NO_WRAP);
    }

    /**
     * 计算合适的采样率以将图片压缩到目标尺寸
     * @param options BitmapFactory.Options
     * @param reqWidth 目标宽度
     * @param reqHeight 目标高度
     * @return 采样率
     */
    public static int calculateInSampleSize(BitmapFactory.Options options, int reqWidth, int reqHeight) {
        final int height = options.outHeight;
        final int width = options.outWidth;
        int inSampleSize = 1;

        if (height > reqHeight || width > reqWidth) {
            final int halfHeight = height / 2;
            final int halfWidth = width / 2;

            while ((halfHeight / inSampleSize) >= reqHeight && (halfWidth / inSampleSize) >= reqWidth) {
                inSampleSize *= 2;
            }
        }

        return inSampleSize;
    }

    /**
     * 压缩Bitmap到指定的最大尺寸
     * @param bitmap 原始位图
     * @param maxWidth 最大宽度
     * @param maxHeight 最大高度
     * @return 压缩后的位图
     */
    public static Bitmap compressBitmap(Bitmap bitmap, int maxWidth, int maxHeight) {
        if (bitmap == null) {
            return null;
        }

        int width = bitmap.getWidth();
        int height = bitmap.getHeight();

        // 如果图片已经小于目标尺寸，直接返回
        if (width <= maxWidth && height <= maxHeight) {
            return bitmap;
        }

        // 计算缩放比例
        float scaleWidth = (float) maxWidth / width;
        float scaleHeight = (float) maxHeight / height;
        float scale = Math.min(scaleWidth, scaleHeight);

        // 计算新的尺寸
        int newWidth = Math.round(width * scale);
        int newHeight = Math.round(height * scale);

        Log.d(TAG, "压缩图片: " + width + "x" + height + " -> " + newWidth + "x" + newHeight);

        // 创建缩放后的位图
        return Bitmap.createScaledBitmap(bitmap, newWidth, newHeight, true);
    }

    /**
     * 创建缩略图
     * @param bitmap 原始位图
     * @param thumbnailSize 缩略图尺寸
     * @return 缩略图位图
     */
    public static Bitmap createThumbnail(Bitmap bitmap, int thumbnailSize) {
        if (bitmap == null) {
            return null;
        }

        return compressBitmap(bitmap, thumbnailSize, thumbnailSize);
    }
}