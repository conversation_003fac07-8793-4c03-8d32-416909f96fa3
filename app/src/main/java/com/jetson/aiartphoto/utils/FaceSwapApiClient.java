package com.jetson.aiartphoto.utils;

import android.util.Log;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import okhttp3.MediaType;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.Callable;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;
import java.util.Map;
import java.util.SortedMap;
import java.util.TreeMap;
import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.nio.ByteBuffer;
import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;

public class FaceSwapApiClient {
    private static final String TAG = "FaceSwapApiClient";
    private static FaceSwapApiClient instance;
    private final ExecutorService executorService;
    private final OkHttpClient httpClient;

    // API认证信息
    private final String accessKey;
    private final String secretKey;
    
    // API端点
    private static final String API_ENDPOINT = "https://visual.volcengineapi.com";
    private static final String API_VERSION = "2022-08-31";
    private static final String REGION = "cn-north-1";
    private static final String SERVICE = "cv";
    private static final String PATH = "/";
    private static final String HOST = "visual.volcengineapi.com";
    
    // URL编码用的BitSet
    private static final BitSet URLENCODER = new BitSet(256);
    private static final String CONST_ENCODE = "0123456789ABCDEF";
    public static final Charset UTF_8 = StandardCharsets.UTF_8;
    
    static {
        int i;
        for (i = 97; i <= 122; ++i) {
            URLENCODER.set(i);
        }
        for (i = 65; i <= 90; ++i) {
            URLENCODER.set(i);
        }
        for (i = 48; i <= 57; ++i) {
            URLENCODER.set(i);
        }
        URLENCODER.set('-');
        URLENCODER.set('_');
        URLENCODER.set('.');
        URLENCODER.set('~');
    }

    private FaceSwapApiClient(String accessKey, String secretKey) {
        this.accessKey = accessKey;
        this.secretKey = secretKey;
        
        this.executorService = Executors.newSingleThreadExecutor();
        
        // 配置OkHttpClient
        this.httpClient = new OkHttpClient.Builder()
            .connectTimeout(30, TimeUnit.SECONDS)
            .readTimeout(30, TimeUnit.SECONDS)
            .writeTimeout(30, TimeUnit.SECONDS)
            .build();
    }

    public static synchronized FaceSwapApiClient getInstance(String accessKey, String secretKey) {
        if (instance == null) {
            instance = new FaceSwapApiClient(accessKey, secretKey);
        }
        return instance;
    }

    /**
     * 调用换脸API
     * @param sourceImageUrl 源图片URL（用户照片）
     * @param templateImageUrl 模板图片URL
     * @return 返回包含结果URL的Future对象
     */
    public Future<String> swapFace(String sourceImageUrl, String templateImageUrl) {
        return executorService.submit(new Callable<String>() {
            @Override
            public String call() throws Exception {
                try {
                    // 构建请求JSON
                    JSONObject req = new JSONObject();
                    req.put("req_key", "aigc_face_swap_v1");
                    
                    // 添加图片URL数组
                    JSONArray imageUrls = new JSONArray();
                    imageUrls.add(sourceImageUrl);
                    imageUrls.add(templateImageUrl);
                    req.put("image_urls", imageUrls);
                    
                    // 添加其他参数
//                    req.put("gpen", 0.8);
//                    req.put("skin", 0.1);
//                    req.put("keep_glass", true);
//                    req.put("return_url", true);
                    
                    // 水印设置
//                    JSONObject logoInfo = new JSONObject();
//                    logoInfo.put("add_logo", false);
//                    req.put("logo_info", logoInfo);
                    
                    // 调用API
                    Log.d(TAG, "发送API请求: " + req.toJSONString());
                    String jsonResponse = callVisualApi(req.toJSONString());
                    Log.d(TAG, "API响应: " + jsonResponse);
                    
                    // 解析响应
                    JSONObject jsonObj = JSON.parseObject(jsonResponse);
                    if (jsonObj.getIntValue("code") == 10000) {
                        JSONObject data = jsonObj.getJSONObject("data");
                        JSONArray resultUrls = data.getJSONArray("image_urls");
                        if (resultUrls != null && !resultUrls.isEmpty()) {
                            return resultUrls.getString(0);
                        }
                    }
                    
                    throw new Exception("API调用失败: " + jsonObj.getString("message"));
                } catch (Exception e) {
                    Log.e(TAG, "换脸API调用错误: " + e.getMessage(), e);
                    throw e;
                }
            }
        });
    }

    /**
     * 上传本地图片并调用换脸API
     * @param sourceImagePath 本地源图片路径
     * @param templateImagePath 本地模板图片路径或assets路径
     * @param context 上下文，用于访问assets
     * @return 返回包含结果URL的Future对象
     */
    public Future<String> swapFaceWithLocalImages(String sourceImagePath, String templateImagePath, android.content.Context context) {
        return executorService.submit(new Callable<String>() {
            @Override
            public String call() throws Exception {
                try {
                    // 构建请求JSON
                    JSONObject req = new JSONObject();
                    req.put("req_key", "aigc_face_swap_v1");
                    // 不要在JSON体中添加Action和Version参数，这些应该只在URL查询参数中
                    
                    // 使用base64编码图片数据
                    JSONArray base64Images = new JSONArray();
                    
                    // 转换源图片为base64
                    String sourceBase64 = BitmapUtils.convertImageToBase64(sourceImagePath);
                    if (sourceBase64 == null) {
                        throw new Exception("源图片转换失败");
                    }
                    base64Images.add(sourceBase64);
                    
                    // 转换模板图片为base64
                    String templateBase64;
                    
                    // 记录开始时间，便于调试
                    long startTime = System.currentTimeMillis();
                    
                    Log.d(TAG, "开始处理模板图片: " + templateImagePath);
                    
                    if (templateImagePath.startsWith("file:///android_asset/")) {
                        // 修复：直接使用assets路径的方式
                        String assetPath = templateImagePath.replace("file:///android_asset/", "");
                        Log.d(TAG, "从assets加载图片: " + assetPath);
                        try {
                            java.io.InputStream is = context.getAssets().open(assetPath);
                            int size = is.available();
                            byte[] buffer = new byte[size];
                            int bytesRead = is.read(buffer);
                            is.close();
                            
                            Log.d(TAG, "读取到图片数据: " + bytesRead + " 字节");
                            
                            if (bytesRead > 0) {
                                templateBase64 = android.util.Base64.encodeToString(buffer, android.util.Base64.NO_WRAP);
                                Log.d(TAG, "Base64编码完成，长度: " + (templateBase64 != null ? templateBase64.length() : 0));
                            } else {
                                throw new Exception("读取模板图片失败: 无数据");
                            }
                        } catch (java.io.IOException e) {
                            Log.e(TAG, "读取assets文件失败: " + e.getMessage(), e);
                            throw new Exception("模板图片读取失败: " + e.getMessage());
                        }
                    } else if (templateImagePath.startsWith("templates/")) {
                        // 如果是相对assets路径（不带file:///android_asset/前缀）
                        Log.d(TAG, "检测到相对assets路径，从assets加载图片: " + templateImagePath);
                        try {
                            java.io.InputStream is = context.getAssets().open(templateImagePath);
                            int size = is.available();
                            byte[] buffer = new byte[size];
                            int bytesRead = is.read(buffer);
                            is.close();
                            
                            Log.d(TAG, "读取到图片数据: " + bytesRead + " 字节");
                            
                            if (bytesRead > 0) {
                                templateBase64 = android.util.Base64.encodeToString(buffer, android.util.Base64.NO_WRAP);
                                Log.d(TAG, "Base64编码完成，长度: " + (templateBase64 != null ? templateBase64.length() : 0));
                            } else {
                                throw new Exception("读取模板图片失败: 无数据");
                            }
                        } catch (java.io.IOException e) {
                            Log.e(TAG, "读取assets文件失败: " + e.getMessage(), e);
                            throw new Exception("模板图片读取失败: " + e.getMessage());
                        }
                    } else {
                        // 从文件读取
                        Log.d(TAG, "从文件路径加载图片: " + templateImagePath);
                        templateBase64 = BitmapUtils.convertImageToBase64(templateImagePath);
                        Log.d(TAG, "文件Base64编码完成，长度: " + (templateBase64 != null ? templateBase64.length() : 0));
                    }
                    
                    // 检查处理结果并记录处理时间
                    long processTime = System.currentTimeMillis() - startTime;
                    Log.d(TAG, "模板图片处理完成，耗时: " + processTime + "ms");
                    
                    if (templateBase64 == null || templateBase64.isEmpty()) {
                        throw new Exception("模板图片转换失败");
                    }
                    
                    base64Images.add(templateBase64);
                    
                    // 添加base64图片数据
                    req.put("binary_data_base64", base64Images);
                    
                    // 添加其他参数
                   req.put("gpen", 0.8);
                   req.put("skin", 0.1);
                   req.put("keep_glass", true);
                   req.put("return_url", true);
                    
                    // 水印设置
                   JSONObject logoInfo = new JSONObject();
                   logoInfo.put("add_logo", false);
                   req.put("logo_info", logoInfo);
                    
                    // 调用API
                    Log.d(TAG, "发送API请求（本地图片）" + req.toJSONString());
                    String jsonResponse = callVisualApi(req.toJSONString());
                    Log.d(TAG, "API响应: " + jsonResponse);
                    
                    // 解析响应
                    JSONObject jsonObj = JSON.parseObject(jsonResponse);
                    if (jsonObj.getIntValue("code") == 10000) {
                        JSONObject data = jsonObj.getJSONObject("data");
                        
                        // 检查是否返回了URL
                        JSONArray resultUrls = data.getJSONArray("image_urls");
                        if (resultUrls != null && !resultUrls.isEmpty()) {
                            return resultUrls.getString(0);
                        }
                        
                        // 如果没有URL但有base64数据
                        JSONArray resultBase64 = data.getJSONArray("binary_data_base64");
                        if (resultBase64 != null && !resultBase64.isEmpty()) {
                            // TODO: 将base64转换为图片文件并返回本地路径
                            // 为简化处理，这里不实现
                            throw new Exception("API返回了base64数据，但当前不支持处理");
                        }
                    }
                    
                    throw new Exception("API调用失败: " + jsonObj.getString("message"));
                } catch (Exception e) {
                    Log.e(TAG, "换脸API调用错误: " + e.getMessage(), e);
                    throw e;
                }
            }
        });
    }
    
    /**
     * 实际调用火山引擎视觉服务API
     * @param jsonPayload API请求的JSON载荷
     * @return API返回的JSON字符串
     * @throws IOException 网络错误
     */
    private String callVisualApi(String jsonPayload) throws IOException {
        try {
            // 创建请求体
            byte[] bodyBytes = jsonPayload.getBytes();
            MediaType mediaType = MediaType.parse("application/json");
            RequestBody body = RequestBody.create( mediaType,bodyBytes);
            
            // 获取当前时间
            java.text.SimpleDateFormat sdf = new java.text.SimpleDateFormat("yyyyMMdd'T'HHmmss'Z'");
            sdf.setTimeZone(java.util.TimeZone.getTimeZone("GMT"));
            String xDate = sdf.format(new java.util.Date());
            String shortDate = xDate.substring(0, 8); // YYYYMMDD
            
            // HTTP方法和内容类型
            String httpMethod = "POST";
            String contentType = "application/json";
            
            // 计算请求体的哈希值 - 严格按照官方示例
            String payloadHash = hashSHA256(bodyBytes);
            Log.d(TAG, "请求体哈希: " + payloadHash);
            
            // 签名相关头部
            String signedHeaders = "host;x-date;x-content-sha256;content-type";
            
            // 构建查询参数Map并按字典序排序 - 与官方示例保持一致
            SortedMap<String, String> queryParams = new TreeMap<>();
            queryParams.put("Action", "CVProcess");
            queryParams.put("Version", API_VERSION);
            
            // 构建查询字符串
            StringBuilder querySB = new StringBuilder();
            for (String key : queryParams.keySet()) {
                if (querySB.length() > 0) {
                    querySB.append("&");
                }
                querySB.append(signStringEncoder(key)).append("=")
                       .append(signStringEncoder(queryParams.get(key)));
            }
            // 删除最后的 & 字符，如果存在
            if (querySB.length() > 0 && querySB.charAt(querySB.length() - 1) == '&') {
                querySB.deleteCharAt(querySB.length() - 1);
            }
            String queryString = querySB.toString();
            Log.d(TAG, "查询字符串: " + queryString);
            
            // 构造规范请求 - 严格按照官方示例格式
            String canonicalRequest = httpMethod + "\n" + 
                                     PATH + "\n" +
                                     queryString + "\n" +
                                     "host:" + HOST + "\n" +
                                     "x-date:" + xDate + "\n" +
                                     "x-content-sha256:" + payloadHash + "\n" +
                                     "content-type:" + contentType + "\n" +
                                     "\n" +
                                     signedHeaders + "\n" +
                                     payloadHash;
            
            Log.d(TAG, "规范请求: \n" + canonicalRequest);
            
            // 计算规范请求的哈希值 - 严格按照官方示例
            String canonicalRequestHash = hashSHA256(canonicalRequest.getBytes());
            Log.d(TAG, "规范请求哈希: " + canonicalRequestHash);
            
            // 构造凭证范围
            String credentialScope = shortDate + "/" + REGION + "/" + SERVICE + "/request";
            
            // 构造待签字符串
            String stringToSign = "HMAC-SHA256" + "\n" + 
                                 xDate + "\n" + 
                                 credentialScope + "\n" + 
                                 canonicalRequestHash;
            
            Log.d(TAG, "待签字符串: \n" + stringToSign);
            
            // 计算签名密钥 - 严格按照官方示例
            byte[] signKey = genSigningSecretKeyV4(secretKey, shortDate, REGION, SERVICE);
            
            // 计算签名 - 严格按照官方示例
            byte[] signatureBytes = hmacSHA256(signKey, stringToSign);
            String signature = bytesToHex(signatureBytes);
            Log.d(TAG, "签名: " + signature);
            
            // 构建授权头
            String authHeader = "HMAC-SHA256" +
                              " Credential=" + accessKey + "/" + credentialScope +
                              ", SignedHeaders=" + signedHeaders +
                              ", Signature=" + signature;
            
            Log.d(TAG, "Authorization: " + authHeader);
            
            // 创建请求
            Request request = new Request.Builder()
                .url(API_ENDPOINT + PATH + "?" + queryString)
                .post(body)
                .addHeader("Authorization", authHeader)
                .addHeader("Content-Type", contentType)
                .addHeader("Host", HOST)
                .addHeader("X-Content-SHA256", payloadHash)
                .addHeader("X-Date", xDate)
                .build();
            
            Log.d(TAG, "完整请求URL: " + API_ENDPOINT + PATH + "?" + queryString);
            Log.d(TAG, "请求头 - Authorization: " + authHeader);
            Log.d(TAG, "请求头 - X-Date: " + xDate);
            Log.d(TAG, "请求头 - X-Content-SHA256: " + payloadHash);
            Log.d(TAG, "请求头 - Content-Type: " + contentType);
            Log.d(TAG, "请求体: " + jsonPayload);
                
            // 执行请求
            try (Response response = httpClient.newCall(request).execute()) {
                if (!response.isSuccessful()) {
                    String responseBody = "无响应体";
                    if (response.body() != null) {
                        responseBody = response.body().string();
                    }
                    Log.e(TAG, "API错误响应：" + responseBody);
                    throw new IOException("意外的响应代码: " + response);
                }
                
                String responseBody = response.body().string();
                return responseBody;
            }
        } catch (Exception e) {
            Log.e(TAG, "API调用异常: " + e.getMessage(), e);
            throw new IOException("API调用失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * URL编码字符串（用于签名）- 严格按照官方示例
     */
    private String signStringEncoder(String source) {
        if (source == null) {
            return null;
        }
        
        StringBuilder buf = new StringBuilder(source.length());
        ByteBuffer bb = UTF_8.encode(source);
        while (bb.hasRemaining()) {
            int b = bb.get() & 255;
            if (URLENCODER.get(b)) {
                buf.append((char) b);
            } else if (b == 32) {
                buf.append("%20");
            } else {
                buf.append("%");
                char hex1 = CONST_ENCODE.charAt(b >> 4);
                char hex2 = CONST_ENCODE.charAt(b & 15);
                buf.append(hex1);
                buf.append(hex2);
            }
        }
        return buf.toString();
    }
    
    /**
     * 获取ISO8601格式的时间戳 - 使用官方示例方式
     */
    private String getISO8601Time() {
        java.text.SimpleDateFormat sdf = new java.text.SimpleDateFormat("yyyyMMdd'T'HHmmss'Z'");
        sdf.setTimeZone(java.util.TimeZone.getTimeZone("GMT"));
        return sdf.format(new java.util.Date());
    }
    
    /**
     * 计算SHA256哈希值的十六进制字符串 - 严格按照官方示例
     */
    public static String hashSHA256(byte[] content) throws Exception {
        try {
            java.security.MessageDigest md = java.security.MessageDigest.getInstance("SHA-256");
            return bytesToHex(md.digest(content));
        } catch (Exception e) {
            throw new Exception(
                    "Unable to compute hash while signing request: "
                            + e.getMessage(), e);
        }
    }
    
    /**
     * 计算HMAC-SHA256 - 严格按照官方示例
     */
    public static byte[] hmacSHA256(byte[] key, String content) throws Exception {
        try {
            Mac mac = Mac.getInstance("HmacSHA256");
            mac.init(new SecretKeySpec(key, "HmacSHA256"));
            return mac.doFinal(content.getBytes());
        } catch (Exception e) {
            throw new Exception(
                    "Unable to calculate a request signature: "
                            + e.getMessage(), e);
        }
    }
    
    /**
     * 生成签名密钥 - 严格按照官方示例
     */
    private byte[] genSigningSecretKeyV4(String secretKey, String date, String region, String service) throws Exception {
        byte[] kDate = hmacSHA256((secretKey).getBytes(), date);
        byte[] kRegion = hmacSHA256(kDate, region);
        byte[] kService = hmacSHA256(kRegion, service);
        return hmacSHA256(kService, "request");
    }
    
    /**
     * 将字节数组转换为十六进制字符串 - 严格按照官方示例
     */
    private static String bytesToHex(byte[] bytes) {
        char[] hexChars = new char[bytes.length * 2];
        for (int i = 0; i < bytes.length; i++) {
            int v = bytes[i] & 0xFF;
            hexChars[i * 2] = CONST_ENCODE.charAt(v >>> 4);
            hexChars[i * 2 + 1] = CONST_ENCODE.charAt(v & 0x0F);
        }
        return new String(hexChars).toLowerCase();
    }
    
    /**
     * BitSet类，用于URL编码
     */
    private static class BitSet {
        private final boolean[] bits;
        
        public BitSet(int size) {
            bits = new boolean[size];
        }
        
        public void set(int index) {
            bits[index] = true;
        }
        
        public boolean get(int index) {
            return index < bits.length && bits[index];
        }
    }
} 