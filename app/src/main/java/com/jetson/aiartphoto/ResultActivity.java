package com.jetson.aiartphoto;

import android.app.PendingIntent;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.graphics.Color;
import android.hardware.usb.UsbConstants;
import android.hardware.usb.UsbDevice;
import android.hardware.usb.UsbDeviceConnection;
import android.hardware.usb.UsbEndpoint;
import android.hardware.usb.UsbInterface;
import android.hardware.usb.UsbManager;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.util.Log;
import android.view.View;
import android.widget.Toast;

import androidx.annotation.NonNull;

import com.bumptech.glide.Glide;
import com.google.zxing.BarcodeFormat;
import com.google.zxing.WriterException;
import com.google.zxing.common.BitMatrix;
import com.google.zxing.qrcode.QRCodeWriter;
import com.jetson.aiartphoto.databinding.ActivityResultBinding;
import com.jetson.aiartphoto.utils.FaceSwapApiClient;

import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.util.HashMap;
import java.util.UUID;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;

import okhttp3.Call;
import okhttp3.Callback;
import okhttp3.MediaType;
import okhttp3.MultipartBody;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;

public class ResultActivity extends BaseActivity {

    private static final String TAG = "ResultActivity";
    private static final String ACTION_USB_PERMISSION = "com.jetson.aiartphoto.USB_PERMISSION";
    private ActivityResultBinding binding;
    private String photoPath;
    private String templateUrl;
    private Handler inactivityHandler;
    private static final long INACTIVITY_TIMEOUT = 120000; // 2分钟 = 120000毫秒
    private static final String API_URL = "https://visual.volcengineapi.com"; // 替换为实际API地址
    
    // 火山引擎API的访问密钥（请替换为实际的密钥）
    private static final String ACCESS_KEY = "AKLTNzM4OTRhZjE5OWM3NGE2ZjllZmJhYmNlMWQwZTdiM2I";
    private static final String SECRET_KEY = "WVRSbU5XWTVNR015T1RSaU5ETXpaRGxrTldJeFptUXpaV1JrWXpBd1ltSQ==";
    
    // API客户端
    private FaceSwapApiClient apiClient;
    
    // USB相关变量
    private UsbManager usbManager;
    private UsbDevice printerDevice;
    private PendingIntent permissionIntent;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        binding = ActivityResultBinding.inflate(getLayoutInflater());
        setContentView(binding.getRoot());

        // 初始化API客户端
        apiClient = FaceSwapApiClient.getInstance(ACCESS_KEY, SECRET_KEY);

        // 获取照片路径和模板URL
        photoPath = getIntent().getStringExtra("photo_path");
        templateUrl = getIntent().getStringExtra("template_url");

        if (photoPath == null || templateUrl == null) {
            Toast.makeText(this, "参数错误", Toast.LENGTH_SHORT).show();
            finish();
            return;
        }

        // 记录收到的模板URL
        Log.d(TAG, "收到模板URL: " + templateUrl);

        // 设置返回按钮点击事件
        binding.backButton.setOnClickListener(v -> {
            Intent intent = new Intent(ResultActivity.this, MainActivity.class);
            intent.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP);
            startActivity(intent);
            finish();
        });

        // 设置打印按钮点击事件
        binding.printButton.setOnClickListener(v -> {
            directPrint();
        });

        // 设置关闭二维码按钮点击事件
        binding.closeQrCodeButton.setOnClickListener(v -> {
            binding.qrCodeContainer.setVisibility(View.GONE);
        });

        // 调用换脸API
        callFaceSwapApi();

        // 初始化不活动计时器
        inactivityHandler = new Handler(Looper.getMainLooper());
        
        // 初始化USB管理器
        usbManager = (UsbManager) getSystemService(Context.USB_SERVICE);
        
        // 注册USB权限广播接收器
        permissionIntent = PendingIntent.getBroadcast(this, 0, new Intent(ACTION_USB_PERMISSION), PendingIntent.FLAG_IMMUTABLE);
        IntentFilter filter = new IntentFilter(ACTION_USB_PERMISSION);
        registerReceiver(usbPermissionReceiver, filter);
    }

    // USB权限广播接收器
    private final BroadcastReceiver usbPermissionReceiver = new BroadcastReceiver() {
        @Override
        public void onReceive(Context context, Intent intent) {
            String action = intent.getAction();
            if (ACTION_USB_PERMISSION.equals(action)) {
                synchronized (this) {
                    UsbDevice device = intent.getParcelableExtra(UsbManager.EXTRA_DEVICE);
                    
                    if (intent.getBooleanExtra(UsbManager.EXTRA_PERMISSION_GRANTED, false)) {
                        if (device != null) {
                            // 获得了USB设备权限，继续打印操作
                            printerDevice = device;
                            processPrinting(device);
                        }
                    } else {
                        Log.d(TAG, "USB权限被拒绝: " + device);
                        Toast.makeText(context, "USB打印机权限被拒绝", Toast.LENGTH_SHORT).show();
                    }
                }
            }
        }
    };

    private void callFaceSwapApi() {
        // 显示加载界面
        binding.loadingContainer.setVisibility(View.VISIBLE);
        
        try {
            // 检查照片文件是否存在
            File photoFile = new File(photoPath);
            if (!photoFile.exists()) {
                Toast.makeText(this, "照片文件不存在", Toast.LENGTH_SHORT).show();
                binding.loadingContainer.setVisibility(View.GONE);
                return;
            }
            
            // 处理模板URL
            // 如果模板URL不是以file:///android_asset/开头，且以templates/开头，则添加file:///android_asset/前缀
            if (!templateUrl.startsWith("file:///android_asset/") && templateUrl.startsWith("templates/")) {
                Log.d(TAG, "转换模板URL格式，从: " + templateUrl);
                templateUrl = "file:///android_asset/" + templateUrl;
                Log.d(TAG, "转换后的模板URL: " + templateUrl);
            }
            
            // 记录正在使用的模板URL
            Log.d(TAG, "调用API使用的最终模板URL: " + templateUrl);
            
            // 调用API - 使用本地图片方法
            Future<String> resultFuture = apiClient.swapFaceWithLocalImages(photoPath, templateUrl, this);
            //Future<String> resultFuture = apiClient.swapFace("https://img0.baidu.com/it/u=3902328359,3828743681&fm=253&fmt=auto&app=120&f=JPEG?w=500&h=750", "https://img2.baidu.com/it/u=795354093,2840326274&fm=253&fmt=auto&app=120&f=JPEG?w=655&h=873");
            
            // 在后台线程中等待结果
            new Thread(() -> {
                try {
                    // 等待结果，最多30秒
                    String resultUrl = resultFuture.get(30, TimeUnit.SECONDS);
                    
                    // 在UI线程中显示结果
                    runOnUiThread(() -> {
                        binding.loadingContainer.setVisibility(View.GONE);
                        
                        // 使用Glide加载结果图片
                        Glide.with(ResultActivity.this)
                                .load(resultUrl)
                                .into(binding.resultImageView);
                    });
                } catch (InterruptedException | ExecutionException | TimeoutException e) {
                    Log.e(TAG, "API调用失败: " + e.getMessage(), e);
                    
                    // 提取详细错误信息
                    String detailedError = e.getMessage();
                    Throwable cause = e.getCause();
                    if (cause != null) {
                        detailedError = cause.getMessage();
                        Log.e(TAG, "原始错误: " + detailedError, cause);
                    }
                    
                    // 在UI线程中显示错误信息
                    final String finalError = detailedError;
                    runOnUiThread(() -> {
                        binding.loadingContainer.setVisibility(View.GONE);
                        Toast.makeText(ResultActivity.this, 
                                "换脸失败: " + finalError, 
                                Toast.LENGTH_LONG).show();
                        
                        // 显示原始照片作为备用
                        displayOriginalImage();
                    });
                }
            }).start();
            
        } catch (Exception e) {
            Log.e(TAG, "调用API时出错: " + e.getMessage(), e);
            binding.loadingContainer.setVisibility(View.GONE);
            Toast.makeText(this, "换脸失败: " + e.getMessage(), Toast.LENGTH_SHORT).show();
            
            // 显示原始照片作为备用
            displayOriginalImage();
        }
    }
    
    private void displayOriginalImage() {
        // 显示原始照片作为备用
        File photoFile = new File(photoPath);
        if (photoFile.exists()) {
            Bitmap photoBitmap = BitmapFactory.decodeFile(photoPath);
            binding.resultImageView.setImageBitmap(photoBitmap);
        } else {
            // 如果原始照片也不存在，尝试显示模板图片
            try {
                if (templateUrl.startsWith("file:///android_asset/")) {
                    // 从assets加载模板图片
                    String assetPath = templateUrl.replace("file:///android_asset/", "");
                    InputStream inputStream = getAssets().open(assetPath);
                    Bitmap templateBitmap = BitmapFactory.decodeStream(inputStream);
                    binding.resultImageView.setImageBitmap(templateBitmap);
                } else {
                    // 使用Glide加载网络图片
                    Glide.with(this)
                            .load(templateUrl)
                            .into(binding.resultImageView);
                }
            } catch (IOException e) {
                Log.e(TAG, "加载备用图片失败: " + e.getMessage(), e);
            }
        }
    }

    // 实际的API调用方法（目前未使用）
    private void realFaceSwapApiCall() {
        // 创建OkHttpClient
        OkHttpClient client = new OkHttpClient.Builder()
                .connectTimeout(30, TimeUnit.SECONDS)
                .readTimeout(30, TimeUnit.SECONDS)
                .writeTimeout(30, TimeUnit.SECONDS)
                .build();

        // 创建请求体
        File photoFile = new File(photoPath);
        MultipartBody.Builder requestBodyBuilder = new MultipartBody.Builder()
                .setType(MultipartBody.FORM)
                .addFormDataPart("photo", photoFile.getName(),
                        RequestBody.create(MediaType.parse("image/jpeg"), photoFile));

        // 根据templateUrl是网络URL还是本地assets路径进行不同处理
        if (templateUrl.startsWith("file:///android_asset/")) {
            // 本地assets路径
            String assetPath = templateUrl.replace("file:///android_asset/", "");
            try {
                InputStream inputStream = getAssets().open(assetPath);
                byte[] bytes = new byte[inputStream.available()];
                inputStream.read(bytes);
                
                // 将template_file作为文件上传
                String fileName = assetPath.substring(assetPath.lastIndexOf("/") + 1);
                requestBodyBuilder.addFormDataPart("template_file", fileName,
                        RequestBody.create(MediaType.parse("image/jpeg"), bytes));
            } catch (IOException e) {
                Log.e(TAG, "读取模板图片失败: " + e.getMessage(), e);
                runOnUiThread(() -> {
                    binding.loadingContainer.setVisibility(View.GONE);
                    Toast.makeText(ResultActivity.this, "读取模板失败", Toast.LENGTH_SHORT).show();
                });
                return;
            }
        } else {
            // 网络URL
            requestBodyBuilder.addFormDataPart("template_url", templateUrl);
        }

        RequestBody requestBody = requestBodyBuilder.build();

        // 创建请求
        Request request = new Request.Builder()
                .url(API_URL)
                .post(requestBody)
                .build();

        // 发送请求
        client.newCall(request).enqueue(new Callback() {
            @Override
            public void onFailure(Call call, IOException e) {
                Log.e(TAG, "API调用失败: " + e.getMessage(), e);
                runOnUiThread(() -> {
                    binding.loadingContainer.setVisibility(View.GONE);
                    Toast.makeText(ResultActivity.this, "网络错误，请重试", Toast.LENGTH_SHORT).show();
                });
            }

            @Override
            public void onResponse(Call call, Response response) throws IOException {
                if (response.isSuccessful()) {
                    // 处理成功响应
                    byte[] responseData = response.body().bytes();
                    Bitmap resultBitmap = BitmapFactory.decodeByteArray(responseData, 0, responseData.length);

                    runOnUiThread(() -> {
                        binding.loadingContainer.setVisibility(View.GONE);
                        binding.resultImageView.setImageBitmap(resultBitmap);
                    });
                } else {
                    // 处理错误响应
                    Log.e(TAG, "API调用失败: " + response.code() + " " + response.message());
                    runOnUiThread(() -> {
                        binding.loadingContainer.setVisibility(View.GONE);
                        Toast.makeText(ResultActivity.this, "服务器错误，请重试", Toast.LENGTH_SHORT).show();
                    });
                }
            }
        });
    }

    private void directPrint() {
        // 显示正在打印提示
        Toast.makeText(this, "正在准备打印...", Toast.LENGTH_SHORT).show();
        
        // 获取所有已连接的USB设备
        HashMap<String, UsbDevice> deviceList = usbManager.getDeviceList();
        
        if (deviceList.isEmpty()) {
            Toast.makeText(this, "未检测到USB打印机", Toast.LENGTH_SHORT).show();
            return;
        }
        
        // 寻找打印机设备
        UsbDevice device = null;
        for (UsbDevice usbDevice : deviceList.values()) {
            // 根据设备类型判断是否为打印机
            if (isPrinterDevice(usbDevice)) {
                device = usbDevice;
                break;
            }
        }
        
        if (device == null) {
            Toast.makeText(this, "未找到USB打印机设备", Toast.LENGTH_SHORT).show();
            return;
        }
        
        // 检查是否有权限访问设备
        if (usbManager.hasPermission(device)) {
            // 已有权限，直接打印
            printerDevice = device;
            processPrinting(device);
        } else {
            // 请求权限
            usbManager.requestPermission(device, permissionIntent);
        }
    }
    
    private void processPrinting(UsbDevice device) {
        // 打开设备连接
        UsbDeviceConnection connection = usbManager.openDevice(device);
        if (connection == null) {
            Toast.makeText(this, "无法打开打印机连接", Toast.LENGTH_SHORT).show();
            return;
        }
        
        // 查找打印机接口和端点
        UsbInterface usbInterface = findPrinterInterface(device);
        if (usbInterface == null) {
            Toast.makeText(this, "未找到打印机接口", Toast.LENGTH_SHORT).show();
            connection.close();
            return;
        }
        
        // 声明接口
        if (!connection.claimInterface(usbInterface, true)) {
            Toast.makeText(this, "无法声明打印机接口", Toast.LENGTH_SHORT).show();
            connection.close();
            return;
        }
        
        // 查找输出端点
        UsbEndpoint outputEndpoint = findOutputEndpoint(usbInterface);
        if (outputEndpoint == null) {
            Toast.makeText(this, "未找到打印机输出端点", Toast.LENGTH_SHORT).show();
            connection.releaseInterface(usbInterface);
            connection.close();
            return;
        }
        
        // 显示正在打印提示
        Toast.makeText(this, "正在打印...", Toast.LENGTH_SHORT).show();
        
        // 在新线程中执行打印操作
        new Thread(() -> {
            try {
                // 获取要打印的图像
                Bitmap printBitmap = getBitmapFromImageView();
                if (printBitmap == null) {
                    runOnUiThread(() -> Toast.makeText(ResultActivity.this, 
                            "无法获取打印图像", Toast.LENGTH_SHORT).show());
                    return;
                }
                
                // 将图像转换为打印数据
                byte[] printData = convertBitmapToPrinterData(printBitmap);
                
                // 发送数据到打印机
                int result = connection.bulkTransfer(outputEndpoint, printData, printData.length, 5000);
                
                // 检查发送结果
                if (result >= 0) {
                    runOnUiThread(() -> Toast.makeText(ResultActivity.this, 
                            "打印成功", Toast.LENGTH_SHORT).show());
                } else {
                    runOnUiThread(() -> Toast.makeText(ResultActivity.this, 
                            "打印失败", Toast.LENGTH_SHORT).show());
                }
            } catch (Exception e) {
                Log.e(TAG, "打印过程中出错: " + e.getMessage(), e);
                runOnUiThread(() -> Toast.makeText(ResultActivity.this, 
                        "打印过程中出错: " + e.getMessage(), Toast.LENGTH_SHORT).show());
            } finally {
                // 释放资源
                connection.releaseInterface(usbInterface);
                connection.close();
            }
        }).start();
    }
    
    // 判断设备是否为打印机
    private boolean isPrinterDevice(UsbDevice device) {
        // 检查设备类是否为打印机类(7)或厂商特定类(255)
        if (device.getDeviceClass() == 7 || device.getDeviceClass() == 255) {
            return true;
        }
        
        // 检查接口类
        for (int i = 0; i < device.getInterfaceCount(); i++) {
            UsbInterface intf = device.getInterface(i);
            if (intf.getInterfaceClass() == 7) { // 打印机类
                return true;
            }
        }
        
        // 可以添加特定打印机的VID/PID判断
        // 例如: if (device.getVendorId() == 0x04b8 && device.getProductId() == 0x0005) return true;
        
        return false;
    }
    
    // 查找打印机接口
    private UsbInterface findPrinterInterface(UsbDevice device) {
        for (int i = 0; i < device.getInterfaceCount(); i++) {
            UsbInterface intf = device.getInterface(i);
            // 检查是否为打印机类接口
            if (intf.getInterfaceClass() == 7) { // 打印机类
                return intf;
            }
        }
        
        // 如果没有找到打印机类接口，返回第一个接口
        if (device.getInterfaceCount() > 0) {
            return device.getInterface(0);
        }
        
        return null;
    }
    
    // 查找输出端点
    private UsbEndpoint findOutputEndpoint(UsbInterface intf) {
        for (int i = 0; i < intf.getEndpointCount(); i++) {
            UsbEndpoint endpoint = intf.getEndpoint(i);
            // 查找输出端点（OUT方向）
            if (endpoint.getDirection() == UsbConstants.USB_DIR_OUT) {
                return endpoint;
            }
        }
        return null;
    }
    
    // 从ImageView获取位图
    private Bitmap getBitmapFromImageView() {
        try {
            // 获取当前显示在ImageView中的图像
            binding.resultImageView.setDrawingCacheEnabled(true);
            Bitmap bitmap = Bitmap.createBitmap(binding.resultImageView.getDrawingCache());
            binding.resultImageView.setDrawingCacheEnabled(false);
            return bitmap;
        } catch (Exception e) {
            Log.e(TAG, "获取图像失败: " + e.getMessage(), e);
            return null;
        }
    }
    
    // 将位图转换为打印机数据
    private byte[] convertBitmapToPrinterData(Bitmap bitmap) {
        // 这里实现彩色照片打印命令
        // 实际应用中需要根据具体打印机型号实现相应的数据转换
        
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        
        try {
            // 初始化打印机
            outputStream.write(new byte[] { 0x1B, 0x40 }); // ESC @
            
            // 调整图像大小以适应打印机
            Bitmap resizedBitmap = resizeBitmap(bitmap, 576); // 常见照片打印机宽度为576点
            
            // 使用彩色打印命令
            byte[] colorPrintData = bitmapToColorPrintData(resizedBitmap);
            outputStream.write(colorPrintData);
            
            // 走纸并切纸（如果打印机支持）
            outputStream.write(new byte[] { 0x1B, 0x64, 0x05 }); // ESC d n (走纸5行)
            outputStream.write(new byte[] { 0x1D, 0x56, 0x41, 0x10 }); // GS V A n (切纸)
            
            return outputStream.toByteArray();
            
        } catch (IOException e) {
            Log.e(TAG, "转换打印数据失败: " + e.getMessage(), e);
            return new byte[0];
        }
    }
    
    // 将位图转换为彩色打印数据
    private byte[] bitmapToColorPrintData(Bitmap bitmap) throws IOException {
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        
        int width = bitmap.getWidth();
        int height = bitmap.getHeight();
        
        // 使用彩色打印命令
        // 这里使用常见的彩色打印命令，可能需要根据具体打印机型号调整
        
        // 打印光栅位图命令
        outputStream.write(0x1D); // GS
        outputStream.write(0x76); // v
        outputStream.write(0x30); // 0
        outputStream.write(0x00); // 普通模式
        
        // 写入宽度（以字节为单位）
        int bytesPerLine = (width * 3 + 7) / 8; // 彩色图像每像素3字节(RGB)
        outputStream.write(bytesPerLine & 0xFF);
        outputStream.write((bytesPerLine >> 8) & 0xFF);
        
        // 写入高度（以点为单位）
        outputStream.write(height & 0xFF);
        outputStream.write((height >> 8) & 0xFF);
        
        // 写入彩色图像数据
        // 将位图转换为打印机可识别的格式
        // 这里简单地将RGB值转换为打印机数据
        for (int y = 0; y < height; y++) {
            for (int x = 0; x < width; x++) {
                int pixel = bitmap.getPixel(x, y);
                int red = Color.red(pixel);
                int green = Color.green(pixel);
                int blue = Color.blue(pixel);
                
                // 写入RGB值
                outputStream.write(red);
                outputStream.write(green);
                outputStream.write(blue);
            }
        }
        
        return outputStream.toByteArray();
    }
    
    // 调整位图大小
    private Bitmap resizeBitmap(Bitmap bitmap, int width) {
        int originalWidth = bitmap.getWidth();
        int originalHeight = bitmap.getHeight();
        
        // 计算新高度，保持宽高比
        int newHeight = (int) (originalHeight * (width / (float) originalWidth));
        
        return Bitmap.createScaledBitmap(bitmap, width, newHeight, true);
    }
    
    // 不再需要黑白转换方法
    // private Bitmap convertToBlackAndWhite(Bitmap bitmap) {
    //     ...
    // }
    
    // 不再需要黑白ESC/POS数据转换方法
    // private byte[] bitmapToESCPOSData(Bitmap bitmap) throws IOException {
    //     ...
    // }

    private void showQrCode() {
        // 生成唯一的照片ID
        String photoId = UUID.randomUUID().toString();
        
        // 生成照片访问URL（实际应用中，这应该是一个真实的URL）
        String photoUrl = "https://aiartphoto.com/photos/" + photoId;
        
        // 生成二维码
        try {
            Bitmap qrCodeBitmap = generateQRCode(photoUrl, 512);
            binding.qrCodeImageView.setImageBitmap(qrCodeBitmap);
            binding.qrCodeContainer.setVisibility(View.VISIBLE);
        } catch (WriterException e) {
            Log.e(TAG, "生成二维码失败: " + e.getMessage(), e);
            Toast.makeText(this, "生成二维码失败", Toast.LENGTH_SHORT).show();
        }
    }

    private Bitmap generateQRCode(String content, int size) throws WriterException {
        QRCodeWriter writer = new QRCodeWriter();
        BitMatrix bitMatrix = writer.encode(content, BarcodeFormat.QR_CODE, size, size);
        
        int width = bitMatrix.getWidth();
        int height = bitMatrix.getHeight();
        Bitmap bitmap = Bitmap.createBitmap(width, height, Bitmap.Config.RGB_565);
        
        for (int x = 0; x < width; x++) {
            for (int y = 0; y < height; y++) {
                bitmap.setPixel(x, y, bitMatrix.get(x, y) ? Color.BLACK : Color.WHITE);
            }
        }
        
        return bitmap;
    }

    @Override
    protected void onResume() {
        super.onResume();
        resetInactivityTimer();
    }

    @Override
    protected void onPause() {
        super.onPause();
        removeInactivityTimer();
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        removeInactivityTimer();
        
        // 注销USB权限广播接收器
        try {
            unregisterReceiver(usbPermissionReceiver);
        } catch (Exception e) {
            // 忽略可能的异常
        }
    }
    
    private void resetInactivityTimer() {
        removeInactivityTimer();
        inactivityHandler.postDelayed(this::returnToSplashScreen, INACTIVITY_TIMEOUT);
    }
    
    private void removeInactivityTimer() {
        inactivityHandler.removeCallbacksAndMessages(null);
    }
    
    private void returnToSplashScreen() {
        Intent intent = new Intent(this, MainActivity.class);
        intent.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP);
        startActivity(intent);
        finish();
    }
} 