package com.jetson.aiartphoto;

import android.content.Intent;
import android.net.Uri;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageButton;
import android.widget.ImageView;
import android.widget.TextView;
import android.widget.Toast;
import android.widget.Button;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.DefaultItemAnimator;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.bumptech.glide.Glide;
import com.bumptech.glide.RequestManager;
import com.bumptech.glide.load.engine.DiskCacheStrategy;
import com.bumptech.glide.request.RequestOptions;
import com.jetson.aiartphoto.databinding.ActivityStyleSelectionBinding;
import com.jetson.aiartphoto.utils.FaceSwapApiClient;

import java.io.IOException;
import java.lang.ref.WeakReference;
import java.util.ArrayList;
import java.util.List;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;

public class StyleSelectionActivity extends BaseActivity {

    private ActivityStyleSelectionBinding binding;
    private CategoryAdapter categoryAdapter;
    private TemplateAdapter templateAdapter;
    private Handler inactivityHandler;
    private static final long INACTIVITY_TIMEOUT = 120000; // 2分钟 = 120000毫秒
    private int selectedTemplatePosition = -1; // 记录选中的模板位置
    private RequestManager glideRequestManager;
    // 新增：声明photoPath为成员变量
    private String photoPath;

    // 全局保存所有生成结果（模板路径->生成结果URL）
    private final Map<String, String> generatedResultMap = new HashMap<>();
    // 多选已选图片URL
    private final Set<String> selectedResultUrls = new HashSet<>();

    private static final String ACCESS_KEY = "AKLTNzM4OTRhZjE5OWM3NGE2ZjllZmJhYmNlMWQwZTdiM2I";
    private static final String SECRET_KEY = "WVRSbU5XWTVNR015T1RSaU5ETXpaRGxrTldJeFptUXpaV1JrWXpBd1ltSQ==";

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        binding = ActivityStyleSelectionBinding.inflate(getLayoutInflater());
        setContentView(binding.getRoot());

        // 校验photo_path参数
        photoPath = getIntent().getStringExtra("photo_path");
        if (photoPath == null || photoPath.isEmpty()) {
            Toast.makeText(this, "缺少照片参数，请重新拍照", Toast.LENGTH_SHORT).show();
            finish();
            return;
        }

        // 初始化Glide RequestManager - 使用Application上下文避免生命周期问题
        glideRequestManager = Glide.with(getApplicationContext());
        
        // 配置RecyclerView以减少闪烁
        configureRecyclerViews();
        
        // 先初始化模板列表
        setupTemplateRecyclerView();
        
        // 再初始化分类列表
        setupCategoryRecyclerView();

        // 设置悬浮按钮
        setupFloatingButtons();

        // 初始化不活动计时器
        inactivityHandler = new Handler(Looper.getMainLooper());
    }
    
    private void configureRecyclerViews() {
        // 禁用动画以减少闪烁
        DefaultItemAnimator animator = new DefaultItemAnimator();
        animator.setSupportsChangeAnimations(false);
        
        binding.templateRecyclerView.setItemAnimator(animator);
        binding.templateRecyclerView.setHasFixedSize(true);
        binding.templateRecyclerView.setItemViewCacheSize(20);
        binding.templateRecyclerView.setDrawingCacheEnabled(true);
        binding.templateRecyclerView.setDrawingCacheQuality(View.DRAWING_CACHE_QUALITY_HIGH);
        
        binding.categoryRecyclerView.setItemAnimator(null);
        binding.categoryRecyclerView.setHasFixedSize(true);
    }

    private void setupFloatingButtons() {
        // 下一步按钮 - 初始状态为禁用
        ImageButton nextButton = findViewById(R.id.nextButton);
        nextButton.setEnabled(false);
        nextButton.setAlpha(0.5f);
        nextButton.setOnClickListener(v -> {
            if (!selectedResultUrls.isEmpty()) {
                // 跳转到打印页面，传递所有已选图片URL
                Intent intent = new Intent(StyleSelectionActivity.this, PrintActivity.class);
                intent.putExtra("selected_urls", selectedResultUrls.toArray(new String[0]));
                startActivity(intent);
            }
        });

        // 返回首页按钮
        ImageButton homeButton = findViewById(R.id.homeButton);
        homeButton.setOnClickListener(v -> {
            // 返回首页
            Intent intent = new Intent(this, MainActivity.class);
            intent.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP);
            startActivity(intent);
            finish();
        });
    }

    private void setupCategoryRecyclerView() {
        List<Category> categories = new ArrayList<>();
        
        try {
            // 从assets目录读取分类文件夹
            String[] categoryDirs = getAssets().list("templates");
            
            if (categoryDirs != null && categoryDirs.length > 0) {
                boolean firstCategory = true;
                for (String dir : categoryDirs) {
                    // 跳过默认模板文件
                    if (dir.contains(".")) continue;
                    
                    categories.add(new Category(dir, firstCategory));
                    firstCategory = false;
                }
            }
        } catch (IOException e) {
            e.printStackTrace();
            // 如果读取失败，使用固定的分类列表
            categories.add(new Category("古装", true));
            categories.add(new Category("现代", false));
            categories.add(new Category("学院", false));
            categories.add(new Category("职业", false));
            categories.add(new Category("民族", false));
        }
        
        // 如果没有找到任何分类，使用固定的分类列表
        if (categories.isEmpty()) {
            categories.add(new Category("古装", true));
            categories.add(new Category("现代", false));
            categories.add(new Category("学院", false));
            categories.add(new Category("职业", false));
            categories.add(new Category("民族", false));
        }

        categoryAdapter = new CategoryAdapter(categories, position -> {
            // 更新选中状态
            for (int i = 0; i < categories.size(); i++) {
                categories.get(i).setSelected(i == position);
            }
            categoryAdapter.notifyDataSetChanged();

            // 根据选中的分类更新模板列表
            updateTemplates(categories.get(position).getName());
            
            // 重置选中的模板
            selectedTemplatePosition = -1;
            updateNextButtonState();
            
            // 重置不活动计时器
            resetInactivityTimer();
        });

        binding.categoryRecyclerView.setLayoutManager(new LinearLayoutManager(this));
        binding.categoryRecyclerView.setAdapter(categoryAdapter);

        // 默认选中第一个分类并加载对应模板
        if (!categories.isEmpty()) {
            updateTemplates(categories.get(0).getName());
        }
    }

    private void setupTemplateRecyclerView() {
        // 修改模板选择逻辑，跳转ResultActivity时传递photo_path和template_url
        templateAdapter = new TemplateAdapter(new ArrayList<>(), position -> {
            // 选中/取消选中逻辑
            Template template = templateAdapter.getItem(position);
            String resultUrl = generatedResultMap.get(template.getImageUrl());
            if (resultUrl != null) {
                if (selectedResultUrls.contains(resultUrl)) {
                    selectedResultUrls.remove(resultUrl);
                } else {
                    selectedResultUrls.add(resultUrl);
                }
                templateAdapter.notifyItemChanged(position);
            }
        }, glideRequestManager, generatedResultMap, selectedResultUrls, this);

        binding.templateRecyclerView.setLayoutManager(new GridLayoutManager(this, 4));
        binding.templateRecyclerView.setAdapter(templateAdapter);
    }

    private void updateNextButtonState() {
        ImageButton nextButton = findViewById(R.id.nextButton);
        if (!selectedResultUrls.isEmpty()) {
            nextButton.setEnabled(true);
            nextButton.setAlpha(1.0f);
        } else {
            nextButton.setEnabled(false);
            nextButton.setAlpha(0.5f);
        }
    }

    private void updateTemplates(String category) {
        List<Template> templates = new ArrayList<>();
        
        try {
            // 从assets目录读取模板图片
            String templatePath = "templates/" + category + "/";
            String[] fileList = getAssets().list(templatePath);
            
            if (fileList != null && fileList.length > 0) {
                for (String fileName : fileList) {
                    if (fileName.toLowerCase().endsWith(".jpg") || 
                        fileName.toLowerCase().endsWith(".jpeg") || 
                        fileName.toLowerCase().endsWith(".png")) {
                        
                        // 构建完整的assets路径
                        String fullPath = templatePath + fileName;
                        templates.add(new Template(fullPath));
                    }
                }
            } else {
                // 如果目录为空或不存在，显示错误信息
                Toast.makeText(this, "未找到" + category + "分类的模板图片", Toast.LENGTH_SHORT).show();
            }
        } catch (IOException e) {
            e.printStackTrace();
            Toast.makeText(this, "加载模板图片失败", Toast.LENGTH_SHORT).show();
        }
        
        // 如果没有找到图片，使用模拟数据（仅用于开发测试）
        if (templates.isEmpty()) {
            for (int i = 1; i <= 10; i++) {
                templates.add(new Template("templates/default_template.jpg"));
            }
        }
        
        templateAdapter.updateData(templates);
    }

    @Override
    protected void onResume() {
        super.onResume();
        resetInactivityTimer();
        
        // 恢复图片加载
        if (glideRequestManager != null) {
            glideRequestManager.resumeRequests();
        }
    }

    @Override
    protected void onPause() {
        super.onPause();
        removeInactivityTimer();
        
        // 暂停图片加载
        if (glideRequestManager != null) {
            glideRequestManager.pauseRequests();
        }
    }

    @Override
    protected void onStop() {
        super.onStop();
        // 在onStop中清理图片加载任务，避免在后台时继续加载
        if (glideRequestManager != null) {
            glideRequestManager.pauseRequests();
        }
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        removeInactivityTimer();
        
        // 清理RecyclerView
        if (binding != null) {
            if (binding.categoryRecyclerView != null) {
                binding.categoryRecyclerView.setAdapter(null);
            }
            if (binding.templateRecyclerView != null) {
                binding.templateRecyclerView.setAdapter(null);
            }
            binding = null;
        }
        
        // 清空适配器数据
        categoryAdapter = null;
        templateAdapter = null;
        
        // 修复Glide资源清理 - 不直接调用onDestroy
        if (glideRequestManager != null) {
            try {
                // 只清理请求，不调用onDestroy
                glideRequestManager.pauseRequests();
                glideRequestManager.clearOnStop();
            } catch (Exception e) {
                e.printStackTrace();
            } finally {
                glideRequestManager = null;
            }
        }
        
        // 强制GC回收
        System.gc();
    }
    
    private void resetInactivityTimer() {
        removeInactivityTimer();
        inactivityHandler.postDelayed(this::returnToSplashScreen, INACTIVITY_TIMEOUT);
    }
    
    private void removeInactivityTimer() {
        inactivityHandler.removeCallbacksAndMessages(null);
    }
    
    private void returnToSplashScreen() {
        Intent intent = new Intent(this, MainActivity.class);
        intent.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP);
        startActivity(intent);
        finish();
    }

    // 分类数据类
    private static class Category {
        private final String name;
        private boolean selected;

        public Category(String name, boolean selected) {
            this.name = name;
            this.selected = selected;
        }

        public String getName() {
            return name;
        }

        public boolean isSelected() {
            return selected;
        }

        public void setSelected(boolean selected) {
            this.selected = selected;
        }
    }

    // 模板数据类
    private static class Template {
        private final String imageUrl;

        public Template(String imageUrl) {
            this.imageUrl = imageUrl;
        }

        public String getImageUrl() {
            return imageUrl;
        }
    }

    // 分类适配器
    private static class CategoryAdapter extends RecyclerView.Adapter<CategoryAdapter.ViewHolder> {
        private final List<Category> categories;
        private final OnItemClickListener listener;

        public CategoryAdapter(List<Category> categories, OnItemClickListener listener) {
            this.categories = categories;
            this.listener = listener;
        }

        @NonNull
        @Override
        public ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
            View view = LayoutInflater.from(parent.getContext()).inflate(R.layout.item_category, parent, false);
            return new ViewHolder(view);
        }

        @Override
        public void onBindViewHolder(@NonNull ViewHolder holder, int position) {
            Category category = categories.get(position);
            holder.categoryName.setText(category.getName());

            // 设置选中状态的样式 - 使用安全的方式设置背景
            if (category.isSelected()) {
                try {
                    // 尝试使用tabtile.png作为背景
                    holder.categoryName.setBackgroundResource(R.drawable.tabtilea);
                    holder.categoryName.setTextColor(0xFFFFFFFF);
                } catch (Exception e) {
                    // 如果资源不可用，回退到默认样式
                    holder.categoryName.setBackgroundColor(0xFFE0E0E0);
                    holder.categoryName.setTextColor(0xFF000000);
                    e.printStackTrace();
                }
            } else {
                holder.categoryName.setBackgroundResource(android.R.color.transparent);
                holder.categoryName.setTextColor(0xFF000000);
            }

            final int itemPosition = position;
            holder.itemView.setOnClickListener(v -> {
                if (listener != null) {
                    listener.onItemClick(itemPosition);
                }
            });
        }

        @Override
        public int getItemCount() {
            return categories.size();
        }

        static class ViewHolder extends RecyclerView.ViewHolder {
            TextView categoryName;

            public ViewHolder(@NonNull View itemView) {
                super(itemView);
                categoryName = itemView.findViewById(R.id.categoryName);
            }
        }
    }

    // 模板适配器
    private static class TemplateAdapter extends RecyclerView.Adapter<TemplateAdapter.ViewHolder> {
        private List<Template> templates;
        private final OnItemClickListener listener;
        private final RequestManager glideRequestManager;
        private final Map<String, String> generatedResultMap;
        private final Set<String> selectedResultUrls;
        private final StyleSelectionActivity activity;
        private static final RequestOptions TEMPLATE_REQUEST_OPTIONS = new RequestOptions()
                .centerCrop()
                .diskCacheStrategy(DiskCacheStrategy.ALL)
                .skipMemoryCache(false);

        public TemplateAdapter(List<Template> templates, OnItemClickListener listener, RequestManager glideRequestManager, Map<String, String> generatedResultMap, Set<String> selectedResultUrls, StyleSelectionActivity activity) {
            this.templates = templates;
            this.listener = listener;
            this.glideRequestManager = glideRequestManager;
            this.generatedResultMap = generatedResultMap;
            this.selectedResultUrls = selectedResultUrls;
            this.activity = activity;
        }

        public void updateData(List<Template> templates) {
            this.templates = templates;
            notifyDataSetChanged();
        }

        public Template getItem(int position) {
            return templates.get(position);
        }

        @NonNull
        @Override
        public ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
            View view = LayoutInflater.from(parent.getContext()).inflate(R.layout.item_template, parent, false);
            return new ViewHolder(view, new WeakReference<>(glideRequestManager));
        }

        @Override
        public void onBindViewHolder(@NonNull ViewHolder holder, int position) {
            Template template = templates.get(position);
            String templatePath = template.getImageUrl();
            String resultUrl = generatedResultMap.get(templatePath);
            if (resultUrl != null) {
                holder.loadImage(Uri.parse(resultUrl));
                holder.aiMask.setVisibility(View.GONE);
                holder.aiGenerateButton.setVisibility(View.GONE);
                // 只用右上角打勾表示选中
                if (selectedResultUrls.contains(resultUrl)) {
                    holder.selectedIndicator.setVisibility(View.VISIBLE);
                } else {
                    holder.selectedIndicator.setVisibility(View.GONE);
                }
                holder.itemView.setBackgroundResource(0);
            } else {
                holder.loadImage(Uri.parse("file:///android_asset/" + templatePath));
                holder.aiMask.setVisibility(View.VISIBLE);
                holder.aiGenerateButton.setVisibility(View.VISIBLE);
                holder.selectedIndicator.setVisibility(View.GONE);
                holder.itemView.setBackgroundResource(0);
            }
            final int itemPosition = position;
            holder.aiGenerateButton.setOnClickListener(v -> {
                holder.aiGenerateButton.setText("生成中...");
                holder.aiGenerateButton.setEnabled(false);
                new Thread(() -> {
                    try {
                        String url = FaceSwapApiClient.getInstance(activity.ACCESS_KEY, activity.SECRET_KEY)
                            .swapFaceWithLocalImages(activity.photoPath, templatePath, activity)
                            .get();
                        activity.runOnUiThread(() -> {
                            generatedResultMap.put(templatePath, url);
                            notifyItemChanged(itemPosition);
                        });
                    } catch (Exception e) {
                        activity.runOnUiThread(() -> {
                            holder.aiGenerateButton.setText("AI生成");
                            holder.aiGenerateButton.setEnabled(true);
                            Toast.makeText(activity, "生成失败: " + e.getMessage(), Toast.LENGTH_SHORT).show();
                        });
                    }
                }).start();
            });
            holder.itemView.setOnClickListener(v -> {
                if (resultUrl != null && listener != null) {
                    listener.onItemClick(itemPosition);
                    activity.updateNextButtonState();
                    notifyItemChanged(itemPosition);
                }
            });
        }

        @Override
        public void onViewRecycled(@NonNull ViewHolder holder) {
            super.onViewRecycled(holder);
            // 清理图片加载
            holder.clearImage();
        }

        @Override
        public int getItemCount() {
            return templates.size();
        }

        static class ViewHolder extends RecyclerView.ViewHolder {
            ImageView templateImage;
            ImageView selectedIndicator;
            View aiMask;
            Button aiGenerateButton;
            WeakReference<RequestManager> glideRequestManagerRef;

            public ViewHolder(@NonNull View itemView, WeakReference<RequestManager> glideRequestManagerRef) {
                super(itemView);
                templateImage = itemView.findViewById(R.id.templateImage);
                selectedIndicator = itemView.findViewById(R.id.selectedIndicator);
                aiMask = itemView.findViewById(R.id.aiMask);
                aiGenerateButton = itemView.findViewById(R.id.aiGenerateButton);
                this.glideRequestManagerRef = glideRequestManagerRef;
            }
            public void loadImage(Uri imageUri) {
                RequestManager glideManager = glideRequestManagerRef.get();
                if (glideManager != null) {
                    glideManager.load(imageUri)
                            .apply(TEMPLATE_REQUEST_OPTIONS)
                            .into(templateImage);
                }
            }
            public void clearImage() {
                RequestManager glideManager = glideRequestManagerRef.get();
                if (glideManager != null) {
                    glideManager.clear(templateImage);
                }
            }
        }
    }

    private interface OnItemClickListener {
        void onItemClick(int position);
    }
} 