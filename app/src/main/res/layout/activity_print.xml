<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:padding="16dp">

    <TextView
        android:id="@+id/printTitle"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:text="打印预览"
        android:textSize="22sp"
        android:textStyle="bold"
        android:gravity="center"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toStartOf="@id/printButtonPanel"
        app:layout_constraintTop_toTopOf="parent"
        android:layout_marginBottom="8dp"/>

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/printImageList"
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintTop_toBottomOf="@id/printTitle"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toStartOf="@id/printButtonPanel"
        android:clipToPadding="false"
        android:padding="0dp"/>

    <LinearLayout
        android:id="@+id/printButtonPanel"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:gravity="center"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        android:layout_marginEnd="8dp">

        <ImageButton
            android:id="@+id/backButton"
            android:layout_width="157dp"
            android:layout_height="69dp"
            android:layout_marginBottom="24dp"
            android:background="@null"
            android:contentDescription="返回"
            android:padding="8dp"
            android:scaleType="centerInside"
            android:src="@drawable/upstep" />

        <ImageButton
            android:id="@+id/homeButton"
            android:layout_width="146dp"
            android:layout_height="54dp"
            android:layout_marginBottom="24dp"
            android:background="@drawable/home"
            android:contentDescription="返回首页"
            android:drawablePadding="8dp"
            android:gravity="center"
            android:padding="8dp"
            android:scaleType="centerInside"
            android:text="返回首页"
            android:textColor="#000"
            android:textSize="16sp" />

        <ImageButton
            android:id="@+id/printButton"
            android:layout_width="147dp"
            android:layout_height="53dp"
            android:background="@drawable/printphoto2"
            android:drawablePadding="8dp"
            android:gravity="center"
            android:padding="8dp"
            android:text="打印"
            android:textColor="#FFFFFF"
            android:textSize="16sp" />
    </LinearLayout>

</androidx.constraintlayout.widget.ConstraintLayout> 