<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:padding="16dp">

    <TextView
        android:id="@+id/printTitle"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:text="打印预览"
        android:textSize="22sp"
        android:textStyle="bold"
        android:gravity="center"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toStartOf="@id/printButtonPanel"
        app:layout_constraintTop_toTopOf="parent"
        android:layout_marginBottom="8dp"/>

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/printImageList"
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintTop_toBottomOf="@id/printTitle"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toStartOf="@id/printButtonPanel"
        android:clipToPadding="false"
        android:padding="0dp"/>

    <LinearLayout
        android:id="@+id/printButtonPanel"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:gravity="center"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        android:layout_marginEnd="8dp">

        <ImageButton
            android:id="@+id/backButton"
            android:layout_width="157dp"
            android:layout_height="69dp"
            android:layout_marginBottom="24dp"
            android:background="@null"
            android:contentDescription="返回"
            android:padding="8dp"
            android:scaleType="centerInside"
            android:src="@drawable/upstep" />

        <ImageButton
            android:id="@+id/homeButton"
            android:layout_width="146dp"
            android:layout_height="54dp"
            android:layout_marginBottom="24dp"
            android:background="@drawable/home"
            android:contentDescription="返回首页"
            android:drawablePadding="8dp"
            android:gravity="center"
            android:padding="8dp"
            android:scaleType="centerInside"
            android:text="返回首页"
            android:textColor="#000"
            android:textSize="16sp" />

        <ImageButton
            android:id="@+id/printButton"
            android:layout_width="147dp"
            android:layout_height="53dp"
            android:background="@drawable/printphoto2"
            android:drawablePadding="8dp"
            android:gravity="center"
            android:padding="8dp"
            android:text="打印"
            android:textColor="#FFFFFF"
            android:textSize="16sp" />
    </LinearLayout>

    <!-- 支付遮罩层 -->
    <RelativeLayout
        android:id="@+id/paymentOverlay"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="#80000000"
        android:clickable="true"
        android:focusable="true"
        android:visibility="visible"
        tools:layout_editor_absoluteX="16dp"
        tools:layout_editor_absoluteY="16dp">

        <ScrollView
            android:layout_width="300dp"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:fillViewport="true"
            android:maxHeight="500dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@drawable/payment_dialog_bg"
                android:orientation="vertical"
                android:padding="12dp">

                <!-- 标题栏，包含标题和关闭按钮 -->
                <RelativeLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="6dp">

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:gravity="center"
                        android:text="微信扫码支付"
                        android:textColor="#333333"
                        android:textSize="16sp"
                        android:textStyle="bold" />

                    <ImageButton
                        android:id="@+id/btnClosePayment"
                        android:layout_width="28dp"
                        android:layout_height="28dp"
                        android:layout_alignParentEnd="true"
                        android:layout_centerVertical="true"
                        android:background="?android:attr/selectableItemBackgroundBorderless"
                        android:contentDescription="关闭"
                        android:padding="2dp"
                        android:src="@drawable/ic_close" />

                </RelativeLayout>

                <!-- 金额显示 -->
                <TextView
                    android:id="@+id/tvPaymentAmount"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="8dp"
                    android:gravity="center"
                    android:text="¥ 5.00"
                    android:textColor="#FF6B35"
                    android:textSize="18sp"
                    android:textStyle="bold" />

                <!-- 二维码容器 -->
                <FrameLayout
                    android:layout_width="130dp"
                    android:layout_height="130dp"
                    android:layout_gravity="center"
                    android:layout_marginBottom="6dp"
                    android:background="@drawable/qr_code_bg">

                    <ImageView
                        android:id="@+id/ivPaymentQRCode"
                        android:layout_width="120dp"
                        android:layout_height="120dp"
                        android:layout_gravity="center"
                        android:scaleType="fitCenter" />

                    <!-- 加载中提示 -->
                    <ProgressBar
                        android:id="@+id/pbPaymentLoading"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center"
                        android:visibility="visible" />

                </FrameLayout>

                <!-- 倒计时显示 -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="6dp"
                    android:gravity="center"
                    android:orientation="horizontal">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="倒计时："
                        android:textColor="#666666"
                        android:textSize="12sp" />

                    <TextView
                        android:id="@+id/tvPaymentCountdown"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="05:00"
                        android:textColor="#FF6B35"
                        android:textSize="14sp"
                        android:textStyle="bold" />

                </LinearLayout>

                <!-- 支付状态提示 -->
                <TextView
                    android:id="@+id/tvPaymentStatusText"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="8dp"
                    android:gravity="center"
                    android:paddingHorizontal="4dp"
                    android:text="请使用微信扫描二维码完成支付"
                    android:textColor="#666666"
                    android:textSize="11sp" />

                <!-- 取消按钮 -->
                <Button
                    android:id="@+id/btnCancelPayment"
                    android:layout_width="match_parent"
                    android:layout_height="36dp"
                    android:layout_marginTop="2dp"
                    android:background="@drawable/button_cancel_bg"
                    android:text="取消支付"
                    android:textColor="#666666"
                    android:textSize="12sp" />

            </LinearLayout>

        </ScrollView>

    </RelativeLayout>

</androidx.constraintlayout.widget.ConstraintLayout>