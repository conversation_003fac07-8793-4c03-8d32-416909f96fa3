<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:padding="4dp">

    <androidx.cardview.widget.CardView
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:cardCornerRadius="8dp"
        app:cardElevation="1dp"
        app:cardPreventCornerOverlap="true"
        app:cardUseCompatPadding="true"
        app:layout_constraintDimensionRatio="11:16"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent">

        <FrameLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <ImageView
                android:id="@+id/templateImage"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:scaleType="centerCrop" 
                android:contentDescription="模板图片" />

            <!-- 半透明遮罩层 -->
            <View
                android:id="@+id/aiMask"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:background="#80000000"
                android:visibility="visible" />

            <!-- AI生成按钮 -->
            <Button
                android:id="@+id/aiGenerateButton"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="AI生成"
                android:textColor="#FFFFFF"
                android:background="@drawable/rounded_button"
                android:alpha="0.9"
                android:layout_gravity="center"
                android:visibility="visible" />

            <!-- 选中状态指示器 -->
            <ImageView
                android:id="@+id/selectedIndicator"
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:layout_gravity="top|end"
                android:layout_margin="4dp"
                android:src="@drawable/ic_check"
                android:background="@drawable/circle_background"
                android:padding="4dp"
                android:visibility="gone"
                android:contentDescription="选中标记" />

        </FrameLayout>
    </androidx.cardview.widget.CardView>

</androidx.constraintlayout.widget.ConstraintLayout> 