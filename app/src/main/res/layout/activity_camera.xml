<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".CameraActivity">

    <!-- 相机预览 -->
    <androidx.camera.view.PreviewView
        android:id="@+id/viewFinder"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <!-- 拍照按钮 -->
    <ImageButton
        android:id="@+id/cameraCaptureButton"
        android:layout_width="80dp"
        android:layout_height="80dp"
        android:layout_marginEnd="50dp"
        android:background="@drawable/capture"
        android:contentDescription="拍照"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <!-- 倒计时文本 -->
    <TextView
        android:id="@+id/countdownText"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textColor="#FFFFFF"
        android:textSize="72sp"
        android:textStyle="bold"
        android:visibility="invisible"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="3" />

    <!-- 照片预览容器 -->
    <FrameLayout
        android:id="@+id/photoPreviewContainer"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="#000000"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <!-- 照片预览 -->
        <ImageView
            android:id="@+id/photoPreview"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:adjustViewBounds="true"
            android:scaleType="fitCenter" />

        <!-- 按钮容器 -->
        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="right|center_vertical"
            android:layout_marginEnd="50dp"
            android:orientation="vertical">

            <!-- 重拍按钮 -->
            <Button
                android:id="@+id/retakeButton"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginBottom="20dp"
                android:text="重拍" />

            <!-- 确认按钮 -->
            <Button
                android:id="@+id/confirmButton"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="确定" />
        </LinearLayout>
    </FrameLayout>

</androidx.constraintlayout.widget.ConstraintLayout> 