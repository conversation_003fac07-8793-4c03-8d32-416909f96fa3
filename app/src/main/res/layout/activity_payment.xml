<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#80000000">

    <LinearLayout
        android:layout_width="300dp"
        android:layout_height="400dp"
        android:layout_centerInParent="true"
        android:background="@drawable/payment_dialog_bg"
        android:orientation="vertical"
        android:padding="20dp">

        <!-- 标题栏，包含标题和关闭按钮 -->
        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="15dp">

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:text="微信扫码支付"
                android:textColor="#333333"
                android:textSize="18sp"
                android:textStyle="bold" />

            <ImageButton
                android:id="@+id/btnClose"
                android:layout_width="32dp"
                android:layout_height="32dp"
                android:layout_alignParentEnd="true"
                android:layout_centerVertical="true"
                android:background="?android:attr/selectableItemBackgroundBorderless"
                android:contentDescription="关闭"
                android:padding="4dp"
                android:src="@drawable/ic_close" />

        </RelativeLayout>

        <!-- 金额显示 -->
        <TextView
            android:id="@+id/tvAmount"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:text="¥ 5.00"
            android:textColor="#FF6B35"
            android:textSize="24sp"
            android:textStyle="bold"
            android:layout_marginBottom="20dp" />

        <!-- 二维码容器 -->
        <FrameLayout
            android:layout_width="200dp"
            android:layout_height="200dp"
            android:layout_gravity="center"
            android:background="@drawable/qr_code_bg"
            android:layout_marginBottom="15dp">

            <ImageView
                android:id="@+id/ivQRCode"
                android:layout_width="180dp"
                android:layout_height="180dp"
                android:layout_gravity="center"
                android:scaleType="fitCenter" />

            <!-- 加载中提示 -->
            <ProgressBar
                android:id="@+id/pbLoading"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:visibility="visible" />

        </FrameLayout>

        <!-- 倒计时显示 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:orientation="horizontal"
            android:layout_marginBottom="15dp">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="支付倒计时："
                android:textColor="#666666"
                android:textSize="14sp" />

            <TextView
                android:id="@+id/tvCountdown"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="05:00"
                android:textColor="#FF6B35"
                android:textSize="16sp"
                android:textStyle="bold" />

        </LinearLayout>

        <!-- 支付状态提示 -->
        <TextView
            android:id="@+id/tvPaymentStatus"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:text="请使用微信扫描二维码完成支付"
            android:textColor="#666666"
            android:textSize="14sp" />

        <!-- 取消按钮 -->
        <Button
            android:id="@+id/btnCancel"
            android:layout_width="match_parent"
            android:layout_height="40dp"
            android:layout_marginTop="10dp"
            android:background="@drawable/button_cancel_bg"
            android:text="取消支付"
            android:textColor="#666666"
            android:textSize="14sp" />

    </LinearLayout>

</RelativeLayout> 