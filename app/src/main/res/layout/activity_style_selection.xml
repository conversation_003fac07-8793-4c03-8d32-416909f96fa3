<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".StyleSelectionActivity">

    <!-- 页面标题 - 艺术字居中 -->
    <TextView
        android:id="@+id/titleText"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/purple_500"
        android:padding="16dp"
        android:text="选择风格"
        android:textColor="#FFFFFF"
        android:textSize="28sp"
        android:textStyle="bold"
        android:gravity="center"
        android:fontFamily="cursive"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <!-- 内容区域 -->
    <LinearLayout
        android:id="@+id/contentLayout"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:orientation="horizontal"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/titleText">

        <!-- 左侧菜单栏 -->
        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/categoryRecyclerView"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:background="#F5F5F5" />

        <!-- 右侧模板图片网格 -->
        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/templateRecyclerView"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="3"
            android:padding="4dp" />

    </LinearLayout>

    <!-- 右侧悬浮按钮 -->
    <LinearLayout
        android:id="@+id/floatingButtonsLayout"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:layout_margin="16dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@+id/contentLayout"
        app:layout_constraintBottom_toBottomOf="@+id/contentLayout">

        <!-- 下一步按钮 -->
        <ImageButton
            android:id="@+id/nextButton"
            android:layout_width="100dp"
            android:layout_height="100dp"
            android:layout_marginBottom="16dp"
            android:contentDescription="下一步"
            android:background="@null"
            android:scaleType="fitCenter"
            android:src="@drawable/nextstepui" />

        <!-- 返回首页按钮 -->
        <ImageButton
            android:id="@+id/homeButton"
            android:layout_width="100dp"
            android:layout_height="100dp"
            android:contentDescription="返回首页"
            android:background="@null"
            android:scaleType="fitCenter"
            android:src="@drawable/returnhome"/>

    </LinearLayout>

    <!-- 加载指示器 -->
    <FrameLayout
        android:id="@+id/loadingContainer"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="#80000000"
        android:clickable="true"
        android:focusable="true"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:background="@drawable/loading_background"
            android:gravity="center"
            android:orientation="vertical"
            android:padding="24dp">

            <ProgressBar
                android:layout_width="48dp"
                android:layout_height="48dp"
                android:indeterminateTint="@color/purple_500" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:text="加载中..."
                android:textColor="#FFFFFF"
                android:textSize="16sp" />
        </LinearLayout>
    </FrameLayout>

</androidx.constraintlayout.widget.ConstraintLayout> 