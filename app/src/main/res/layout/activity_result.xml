<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".ResultActivity">

    <!-- 结果图片带相框 -->
    <FrameLayout
        android:id="@+id/imageFrameContainer"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_margin="16dp"
        android:background="@drawable/photo_frame"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <ImageView
            android:id="@+id/resultImageView"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:scaleType="fitCenter"
            android:contentDescription="结果图片" />

    </FrameLayout>

    <!-- 右侧悬浮按钮容器 -->
    <LinearLayout
        android:id="@+id/buttonContainer"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:layout_marginEnd="16dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <!-- 返回按钮 -->

        <!-- 打印按钮 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:orientation="horizontal">

            <ImageButton
                android:id="@+id/printButton"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:adjustViewBounds="true"
                android:background="@null"
                android:contentDescription="扫码打印"
                android:maxWidth="75dp"
                android:maxHeight="75dp"
                android:scaleType="fitCenter"
                android:src="@drawable/printphoto" />
        </LinearLayout>

        <Space
            android:layout_width="match_parent"
            android:layout_height="22dp" />

        <ImageButton
            android:id="@+id/backButton"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:adjustViewBounds="true"
            android:background="@null"
            android:contentDescription="返回"
            android:maxWidth="75dp"
            android:maxHeight="75dp"
            android:scaleType="fitCenter"
            android:src="@drawable/returnhome" />
    </LinearLayout>

    <!-- 二维码容器 -->
    <FrameLayout
        android:id="@+id/qrCodeContainer"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="#80000000"
        android:clickable="true"
        android:focusable="true"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <LinearLayout
            android:layout_width="300dp"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:background="#FFFFFF"
            android:gravity="center"
            android:orientation="vertical"
            android:padding="24dp">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                android:text="扫描二维码获取照片"
                android:textColor="#000000"
                android:textSize="18sp"
                android:textStyle="bold" />

            <ImageView
                android:id="@+id/qrCodeImageView"
                android:layout_width="200dp"
                android:layout_height="200dp"
                android:contentDescription="二维码" />

            <Button
                android:id="@+id/closeQrCodeButton"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:text="关闭" />
        </LinearLayout>
    </FrameLayout>

    <!-- 加载指示器 -->
    <FrameLayout
        android:id="@+id/loadingContainer"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="#80000000"
        android:clickable="true"
        android:focusable="true"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:background="@drawable/loading_background"
            android:gravity="center"
            android:orientation="vertical"
            android:padding="24dp">

            <ProgressBar
                android:layout_width="48dp"
                android:layout_height="48dp"
                android:indeterminateTint="@color/purple_500" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:text="加载中..."
                android:textColor="#FFFFFF"
                android:textSize="16sp" />
        </LinearLayout>
    </FrameLayout>

</androidx.constraintlayout.widget.ConstraintLayout> 