<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <!-- 添加相机权限 -->
    <uses-permission android:name="android.permission.CAMERA" />
    <!-- 添加存储权限 -->
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
    <!-- 添加网络权限 -->
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <!-- 添加相机特性声明 -->
    <uses-feature android:name="android.hardware.camera" android:required="true" />
    <!-- 添加USB打印机所需的权限 -->
    <uses-feature android:name="android.hardware.usb.host" />

    <application
        android:allowBackup="true"
        android:dataExtractionRules="@xml/data_extraction_rules"
        android:fullBackupContent="@xml/backup_rules"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:roundIcon="@mipmap/ic_launcher_round"
        android:supportsRtl="true"
        android:theme="@style/Theme.AiartPhoto"
        tools:targetApi="31">
        <!-- 补全打印机服务声明，和厂家demo保持一致 -->
        <service
            android:name="com.hiti.usb.service.PrinterService"
            android:exported="true" />
        <activity
            android:name=".MainActivity"
            android:exported="true"
            android:screenOrientation="portrait">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>
        <activity android:name=".CameraActivity" android:screenOrientation="portrait" />
        <activity android:name=".StyleSelectionActivity" android:screenOrientation="portrait" />
        <activity android:name=".ResultActivity" android:screenOrientation="portrait" />
        <activity android:name=".PrintActivity" android:screenOrientation="portrait" />
        <activity android:name=".PaymentActivity" android:screenOrientation="portrait" />
    </application>

</manifest>