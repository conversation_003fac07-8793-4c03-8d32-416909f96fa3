plugins {
    alias(libs.plugins.android.application)
}

android {
    namespace 'com.jetson.aiartphoto'
    compileSdk 35

    defaultConfig {
        applicationId "com.jetson.aiartphoto"
        minSdk 24
        targetSdk 35
        versionCode 1
        versionName "1.0"

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
    }
//    packagingOptions {
//        exclude 'org/apache/httpcomponents/**' // 排除所有 Apache HTTP 类
//        exclude 'org/apache/http/**' // 排除所有 Apache HTTP 类
//    }

//    configurations.all {
//        resolutionStrategy {
//            force 'org.apache.httpcomponents:httpcore:4.4.4' // 统一版本
//        }
//    }


    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_11
        targetCompatibility JavaVersion.VERSION_11
    }
    
    buildFeatures {
        viewBinding true
    }
    
    // 添加打包选项，解决META-INF文件冲突
    packaging {
        resources {
            excludes += [
                'META-INF/DEPENDENCIES',
                'META-INF/LICENSE',
                'META-INF/LICENSE.txt',
                'META-INF/license.txt',
                'META-INF/NOTICE',
                'META-INF/NOTICE.txt',
                'META-INF/notice.txt',
                'META-INF/ASL2.0',
                'META-INF/*.kotlin_module',
                'org/apache/http/entity/mime/version.properties'
            ]
        }
    }
}
dependencies {
    // 基础依赖
    implementation libs.appcompat
    implementation libs.material
    implementation libs.activity
    implementation libs.constraintlayout
    
    // CameraX
    def camerax_version = "1.3.1"
    implementation "androidx.camera:camera-core:${camerax_version}"
    implementation "androidx.camera:camera-camera2:${camerax_version}"
    implementation "androidx.camera:camera-lifecycle:${camerax_version}"
    implementation "androidx.camera:camera-view:${camerax_version}"
    
    // Glide图片加载
    implementation 'com.github.bumptech.glide:glide:4.16.0'
    
    // Retrofit网络请求
    implementation 'com.squareup.retrofit2:retrofit:2.9.0'
    implementation 'com.squareup.retrofit2:converter-gson:2.9.0'
    implementation 'com.squareup.okhttp3:okhttp:4.12.0'
    implementation 'com.squareup.okhttp3:logging-interceptor:4.12.0'
//
    // ZXing二维码生成
    implementation 'com.google.zxing:core:3.5.2'
    
    // 火山引擎SDK依赖
    // 注意：由于我们无法确认具体的依赖名称，请根据实际情况选择以下其中一种方式
    
    // 选项1: 使用标准Maven仓库中的火山引擎SDK
    implementation('com.volcengine:volc-sdk-java:1.0.25') {
        exclude group: 'org.apache.httpcomponents', module: 'httpcore'
        exclude group: 'org.apache.httpcomponents', module: 'httpclient'
        exclude group: 'org.apache.httpcomponents', module: 'httpmime'
    }
    
    // 选项2: 如果需要引入特定的火山引擎视觉服务模块
    // 请参考火山引擎官方文档获取正确的依赖名称
    // implementation 'com.volcengine:volcengine-java-sdk-visual:x.x.x'
    
    // Fastjson
    implementation 'com.alibaba:fastjson:1.2.83'
    
    // 测试依赖
    testImplementation libs.junit
    androidTestImplementation libs.ext.junit
    androidTestImplementation libs.espresso.core

    // 打印机依赖
//    implementation fileTree(dir: 'libs', include: ['*.jar'])
//    implementation(files('libs/printerService-release.aar')) {
//        exclude group: 'org.apache.httpcomponents', module: 'httpcore'
//    }
//
//    implementation 'org.apache.httpcomponents:httpcore:4.4.4'
//    implementation 'org.apache.httpcomponents:httpclient:4.5.13'

    //implementation fileTree(dir: 'libs', include: ['*.jar'])

    implementation 'androidx.appcompat:appcompat:1.1.0'
    implementation 'androidx.constraintlayout:constraintlayout:1.1.3'
    testImplementation 'junit:junit:4.12'
    androidTestImplementation 'androidx.test.ext:junit:1.1.1'
    androidTestImplementation 'androidx.test.espresso:espresso-core:3.2.0'
//    implementation files('libs\\printerService-release.aar')
//    {
//        exclude module: 'http'
//    }
    //打印机依赖
    implementation files('libs/printerService-release.aar')

}